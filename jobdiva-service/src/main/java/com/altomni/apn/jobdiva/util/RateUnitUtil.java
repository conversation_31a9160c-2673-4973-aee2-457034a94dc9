package com.altomni.apn.jobdiva.util;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.EnumMap;
import java.util.Objects;

@UtilityClass
public class RateUnitUtil {

    static final EnumMap<RateUnitType, BigDecimal> map = new EnumMap<>(RateUnitType.class);

    static {
        map.put(RateUnitType.YEARLY, BigDecimal.valueOf(2080));
        map.put(RateUnitType.MONTHLY, BigDecimal.valueOf(2080));
        map.put(RateUnitType.WEEKLY, BigDecimal.valueOf(40));
        map.put(RateUnitType.DAILY, BigDecimal.valueOf(8));
    }

    public BigDecimal rateConvert(BigDecimal rate, RateUnitType rateUnitType) {
        if (map.containsKey(rateUnitType)) {
            BigDecimal total = rate;
            BigDecimal divisor = map.get(rateUnitType);
            if (Objects.equals(rateUnitType, RateUnitType.MONTHLY)) {
                total = rate.multiply(BigDecimal.valueOf(12));
            }
            return total.divide(divisor, 2, RoundingMode.HALF_UP);
        }
        return rate;
    }

}

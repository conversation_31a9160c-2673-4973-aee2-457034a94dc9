package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessIpgOfferAccept;
import com.altomni.apn.application.dto.CongratsContent;
import com.altomni.apn.application.dto.CongratsUserTeam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * Spring Data SQL repository for the TalentRecruitmentProcessIpgOfferAccept entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessIpgOfferAcceptRepository extends JpaRepository<TalentRecruitmentProcessIpgOfferAccept, Long> {

    TalentRecruitmentProcessIpgOfferAccept findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcessIpgOfferAccept t SET t.lastModifiedDate = current_timestamp, t.lastModifiedBy = ?2 WHERE t.talentRecruitmentProcessId = ?1")
    void updateLastModifiedDateAndLastModifiedBy(Long talentRecruitmentProcessId, String updatedBy);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcessIpgOfferAccept t SET t.note = ?2, t.noteLastModifiedDate = current_timestamp, t.noteLastModifiedByUserId = ?3 WHERE t.talentRecruitmentProcessId = ?1")
    void updateNoteOnly(Long talentRecruitmentProcessId, String note, Long userId);

    List<TalentRecruitmentProcessIpgOfferAccept> findAllByTalentRecruitmentProcessIdIn(List<Long> recruitmentProcessIds);

    @Query(value = "select from_usd_rate_mid from currency_rate_day where currency_id=:currencyId order by id desc limit 1", nativeQuery = true)
    BigDecimal getCurrencyRate(@Param("currencyId") Integer currencyId);

    @Query(value = "select ut.user_id userId, u.first_name leaderName from permission_user_team ut " +
            "left join permission_team t on ut.team_id=t.id " +
            "left join permission_team t2 on t.code like concat(t2.code,'%') " +
            "left join permission_team_leader tl on tl.team_id=t2.id " +
            "left join user u on u.id=tl.user_id " +
            "where ut.user_id in :userIds and ut.is_primary=true and ut.tenant_id=:tenantId and t2.level=1 and tl.user_id is not null", nativeQuery = true)
    List<CongratsUserTeam> getSecondLevelTeamLeaders(@Param("userIds") Collection<Long> userIds, @Param("tenantId") Long tenantId);

    @Query(value = "select onboard.onboard_date onboardDate, onboard.currency, ifnull(fte_fee.total_amount, contract_fee.gp) feeCharge, t.id talentId, t.full_name talentName " +
            "from talent_recruitment_process_onboard_date onboard " +
            "left join talent_recruitment_process_offer_fee_charge fte_fee on onboard.talent_recruitment_process_id=fte_fee.talent_recruitment_process_id " +
            "left join talent_recruitment_process_ipg_contract_fee_charge contract_fee on contract_fee.talent_recruitment_process_id=onboard.talent_recruitment_process_id " +
            "left join talent_recruitment_process p on p.id=onboard.talent_recruitment_process_id " +
            "left join talent t on t.id=p.talent_id " +
            "where onboard.talent_recruitment_process_id=:talentRecruitmentProcessId", nativeQuery = true)
    List<CongratsContent> getCongratsContent(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);
}

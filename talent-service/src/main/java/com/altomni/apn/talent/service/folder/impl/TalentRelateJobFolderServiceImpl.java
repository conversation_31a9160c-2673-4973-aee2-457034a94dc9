package com.altomni.apn.talent.service.folder.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.constants.ResponsibilityConstants;
import com.altomni.apn.common.domain.dict.EnumUserResponsibility;
import com.altomni.apn.common.domain.enumeration.RecommendFeedbackReason;
import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderStatus;
import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderType;
import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderUserRole;
import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderUserType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.folder.JobSharingPlatformTalentRelatedJobFolderRelation;
import com.altomni.apn.common.domain.job.JobNote;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.domain.talent.TalentAssociationJobFolder;
import com.altomni.apn.common.domain.talent.TalentAssociationJobFolderTalent;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.RecommendFeedback;
import com.altomni.apn.common.dto.folder.talentrelatejob.*;
import com.altomni.apn.common.dto.user.RelateJobFolderUserInfo;
import com.altomni.apn.common.enumeration.permission.DataScope;
import com.altomni.apn.common.errors.BadRequestAlertException;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalInterfaceException;
import com.altomni.apn.common.repository.talent.JobSharingPlatformRelatedFolderRepository;
import com.altomni.apn.common.repository.talent.TalentRelateJobFolderRepository;
import com.altomni.apn.common.repository.talent.TalentRelateJobFolderTalentRepository;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.talent.AddTalentsToFoldersOutput;
import com.altomni.apn.job.repository.job.JobNoteRepository;
import com.altomni.apn.job.repository.job.JobRepository;
import com.altomni.apn.job.web.rest.vm.RelateJobFolderJobInfoVM;
import com.altomni.apn.talent.constants.RedisConstants;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.elastic.EsFillerTalentService;
import com.altomni.apn.talent.service.folder.TalentRelateJobFolderService;
import com.altomni.apn.talent.service.talent.TalentOwnershipService;
import com.altomni.apn.talent.service.talent.TalentServiceV3;
import com.altomni.apn.talent.web.rest.talent.dto.TalentsToFoldersDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserTeamPermissionVM;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TalentRelateJobFolderServiceImpl implements TalentRelateJobFolderService {
    @Resource
    private TalentRelateJobFolderRepository talentRelateJobFolderRepository;

    @Resource
    private TalentRelateJobFolderTalentRepository talentRelateJobFolderTalentRepository;
    @Resource
    private JobRepository jobRepository;

    @Resource
    private JobNoteRepository jobNoteRepository;

    @Resource
    private JobSharingPlatformRelatedFolderRepository jobSharingPlatformRelatedFolderRepository;

    @Resource
    private EsFillerTalentService esFillerTalentService;

    @Resource
    private TalentOwnershipService talentOwnershipService;

    @Override
    @Transactional
    public TalentRelateJobFolderWithCreatedFlag addRelateJobFolders(TalentRelateJobFoldersDTO talentRelateJobFoldersDTO) throws Exception {
        Long jobId = talentRelateJobFoldersDTO.getJobId();
        if (jobId == null) {
            throw new CustomParameterizedException("Job id cannot be empty!");
        }
        if (talentRelateJobFoldersDTO.getOwner() != null && talentRelateJobFoldersDTO.getShare() != null) {
            List<Long> owner = talentRelateJobFoldersDTO.getOwner().stream().map(RelateJobFolderUserInfo::getId).collect(Collectors.toList());
            List<Long> share = talentRelateJobFoldersDTO.getShare().stream().map(RelateJobFolderUserInfo::getId).collect(Collectors.toList());
            Collection<Long> intersection = CollUtil.intersection(owner, share);
            if (!intersection.isEmpty()) {
                throw new CustomParameterizedException("Users cannot be both owners and shares!");
            }
        }
        StopWatch stopWatch = new StopWatch("addRelateJobFolders");
        stopWatch.start("[1] getTalentRelateJobFolderByJobIdIsAndUserIdIsAndStatusIs");
        TalentRelateJobFolderWithCreatedFlag ret = new TalentRelateJobFolderWithCreatedFlag();
        TalentAssociationJobFolder folder = talentRelateJobFolderRepository.getTalentRelateJobFolderByJobIdIsAndUserIdIsAndStatusIs(jobId, SecurityUtils.getUserId(), RelateJobFolderStatus.CONFIRM);
        if (folder == null) {
            talentRelateJobFoldersDTO.excludeSelf(SecurityUtils.getUserId());
            folder = new TalentAssociationJobFolder();
            folder.setJobId(jobId);
            folder.setFolderId(IdUtil.simpleUUID());
            folder.setUserId(SecurityUtils.getUserId());
            folder.setRole(RelateJobFolderUserRole.OWNER);
            folder.setStatus(RelateJobFolderStatus.CONFIRM);
            folder = talentRelateJobFolderRepository.saveAndFlush(folder);
            List<TalentAssociationJobFolder> addList = getAddTalentAssociationJobFolder(talentRelateJobFoldersDTO.getOwner(), talentRelateJobFoldersDTO.getShare(), jobId, folder.getFolderId());
            talentRelateJobFolderRepository.saveAllAndFlush(addList);
            stopWatch.stop();
            stopWatch.start("[2] updateTalentRelateJobFolder");
            addList.add(folder);
            esFillerTalentService.updateTalentRelateJobFolder(SecurityUtils.getTenantId(), jobId, folder.getFolderId(), getUpdateTalentRelateJobFolderBodyByAdd(addList));
            ret.setNewlyCreated(true);
        }
        AddTalentsToFoldersOutput addTalentsToFoldersOutput = null;
        if (talentRelateJobFoldersDTO.getTalentIds() != null) {
            stopWatch.stop();
            stopWatch.start("[3] addTalentsToFolder");
            addTalentsToFoldersOutput = addTalentsToFolder(folder.getFolderId(), talentRelateJobFoldersDTO.getTalentIds());
        }
        stopWatch.stop();
        stopWatch.start("[4] getTalentRelateJobFoldersDTO");
        TalentRelateJobFoldersDTO talentRelateJobFolder = new TalentRelateJobFoldersDTO();
        //getTalentRelateJobFoldersDTO(folder.getFolderId(), SecurityUtils.getUserId());
        if (addTalentsToFoldersOutput != null) {
            talentRelateJobFolder.setTalentsToFoldersInfo(addTalentsToFoldersOutput);
        }
        BeanUtil.copyProperties(talentRelateJobFolder, ret);
        stopWatch.stop();
        stopWatch.start("[5] addRecommendFeedback");
        addRecommendFeedback(talentRelateJobFoldersDTO.getRecommendFeedback());
        stopWatch.stop();
        log.info("[apn @{}] addRelateJobFolders time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return ret;
    }

    @Resource
    private TalentServiceV3 talentServiceV3;

    private void addRecommendFeedback(RecommendFeedback recommendFeedback) {
        if(recommendFeedback == null) {
            return;
        }
        recommendFeedback.setReason(RecommendFeedbackReason.ADD_TO_ASSOCIATION_JOB_FOLDER);
        talentServiceV3.recordTalentJobRecommend(recommendFeedback);
    }

    private Instant getMinCreateTime(List<TalentAssociationJobFolder> folders) {
        TalentAssociationJobFolder minFolder = folders.stream()
                .min(Comparator.comparing(TalentAssociationJobFolder::getCreatedDate))
                .orElse(null);
        return minFolder != null ? minFolder.getCreatedDate() : null;
    }

    @Resource
    private EnumCommonService enumCommonService;

    private String getUpdateTalentRelateJobFolderBodyByAdd(List<TalentAssociationJobFolder> folder) {
        Instant minCreateTime = getMinCreateTime(folder);

        JSONObject body = new JSONObject();
        body.put("folderCreatedDate", DateUtil.fromInstantToUtcDateTime(minCreateTime));
        body.put("jobFolderResponsibility", getJobFolderResponsibility(folder));
        return body.toString();
    }

    private String getUpdateTalentRelateJobFolderBody(String folderId) {
        List<TalentAssociationJobFolder> folder = talentRelateJobFolderRepository.getTalentRelateJobFoldersByFolderIdIs(folderId);
        Instant minCreateTime = getMinCreateTime(folder);

        JSONObject body = new JSONObject();
        body.put("folderCreatedDate", DateUtil.fromInstantToUtcDateTime(minCreateTime));
        body.put("jobFolderResponsibility", getJobFolderResponsibility(folder));
        return body.toString();
    }

    private String getResponsibilityEsKey(String esKey) {
        if (esKey.contains(".")) {
            return esKey.substring(esKey.lastIndexOf(".") + 1, esKey.length());
        }
        return esKey;
    }

    private JSONObject getJobFolderResponsibility(List<TalentAssociationJobFolder> folders) {
        List<EnumUserResponsibility> userResponsibilities = enumCommonService.findFoldersOfPreSubmitTalentsEnumUserResponsibility();
        List<Long> userIds = folders.stream().map(TalentAssociationJobFolder::getUserId).toList();
        List<User> userList = userService.findByIds(userIds).getBody();
        Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, u -> u));
        JSONObject jsonObject = new JSONObject();
        for (EnumUserResponsibility userResponsibility : userResponsibilities) {
            String esKey = getResponsibilityEsKey(userResponsibility.getFoldersOfPreSubmitTalentsEsKey());
            switch (userResponsibility.getLabel()) {
                case ResponsibilityConstants.OWNER:
                    List<Long> ownerUserId = folders.stream().filter(p -> RelateJobFolderUserRole.OWNER.equals(p.getRole())).map(TalentAssociationJobFolder::getUserId).collect(Collectors.toList());
                    if (!ownerUserId.isEmpty()) {
                        jsonObject.put(esKey, packageIdListToIdAndNameJSONArray(ownerUserId, userMap));
                    }
                    break;
                case ResponsibilityConstants.SHARED_BY:
                    List<Long> shareUserId = folders.stream().filter(p -> RelateJobFolderUserRole.SHARER.equals(p.getRole())).map(TalentAssociationJobFolder::getUserId).collect(Collectors.toList());
                    if (!shareUserId.isEmpty()) {
                        jsonObject.put(esKey, packageIdListToIdAndNameJSONArray(shareUserId, userMap));
                    }
                    break;
                case ResponsibilityConstants.PENDING:
                    List<Long> pendingUserId = folders.stream().filter(p -> RelateJobFolderStatus.PENDING.equals(p.getStatus())).map(TalentAssociationJobFolder::getUserId).collect(Collectors.toList());
                    if (!pendingUserId.isEmpty()) {
                        jsonObject.put(esKey, packageIdListToIdAndNameJSONArray(pendingUserId, userMap));
                    }
                    break;
            }
        }

        return jsonObject;
    }

    private JSONArray packageIdListToIdAndNameJSONArray(List<Long> idList, Map<Long,User> userMap) {
        JSONArray array = new JSONArray();
        idList.forEach(id -> {
            JSONObject object = new JSONObject();
            object.put("id", String.valueOf(id));
            if(userMap.containsKey(id)){
               User user =  userMap.get(id);
                object.put("name", CommonUtils.formatFullName(user.getFirstName(), user.getLastName()));
            }else{
                object.put("name", "");
            }
            array.add(object);
        });
        return array;
    }

    private JSONArray packageIdListToIdAndNameJSONArray(List<Long> idList) {
        List<User> userList = userService.findByIds(idList).getBody();
        JSONArray array = new JSONArray();
        idList.forEach(id -> {
            JSONObject object = new JSONObject();
            object.put("id", String.valueOf(id));
            String name = getNameByUserListAndUserId(userList, id);
            object.put("name", name);

            array.add(object);
        });
        return array;
    }

    public static String getNameByUserListAndUserId(List<User> userList, Long userId) {
        String userName = "";
        if (CollUtil.isEmpty(userList)) {
            return userName;
        }
        if (userList.stream().anyMatch(u -> u.getId().equals(userId))) {
            Optional<User> userOptional = userList.stream().filter(u -> u.getId().equals(userId)).findFirst();
            User user = new User();
            if (userOptional.isPresent()) {
                user = userOptional.get();
            }
            userName = CommonUtils.formatFullName(user.getFirstName(), user.getLastName());
        }
        return userName;
    }

    @Resource
    private RedissonClient redissonClient;

    @Override
    @Transactional
    public TalentRelateJobFoldersDTO editRelateJobFolders(String folderId, TalentRelateJobFoldersDTO talentRelateJobFoldersDTO) {
        if (StrUtil.isEmpty(folderId)) {
            throw new CustomParameterizedException("Folder id cannot be empty!");
        }
        Long jobId = talentRelateJobFoldersDTO.getJobId();
        if (jobId == null) {
            throw new CustomParameterizedException("Job id cannot be empty!");
        }
        TalentAssociationJobFolder exist = talentRelateJobFolderRepository.getTalentRelateJobFolderByJobIdIsAndFolderIdIsAndUserIdIs(jobId, folderId, SecurityUtils.getUserId());
        if (exist == null) {
            throw new CustomParameterizedException("Folder not exist!");
        }
        if (exist.getStatus() != RelateJobFolderStatus.CONFIRM) {
            throw new CustomParameterizedException("You cannot edit unconfirmed folders!");
        }
        if (exist.getRole() != RelateJobFolderUserRole.OWNER) {
            throw new CustomParameterizedException("You cannot edit folders authority!");
        }
        if (talentRelateJobFoldersDTO.getOwner() != null && talentRelateJobFoldersDTO.getShare() != null) {
            List<Long> owner = talentRelateJobFoldersDTO.getOwner().stream().map(RelateJobFolderUserInfo::getId).collect(Collectors.toList());
            List<Long> share = talentRelateJobFoldersDTO.getShare().stream().map(RelateJobFolderUserInfo::getId).collect(Collectors.toList());
            Collection<Long> intersection = CollUtil.intersection(owner, share);
            if (!intersection.isEmpty()) {
                throw new CustomParameterizedException("Users cannot be both owners and shares!");
            }
        }
        List<TalentAssociationJobFolder> talentRelateJobFoldersByFolderIdIs = talentRelateJobFolderRepository.getTalentRelateJobFoldersByFolderIdIs(folderId);
        //db查出的数据排除当前user
        //传入数据排除当前user
        talentRelateJobFoldersByFolderIdIs = talentRelateJobFoldersByFolderIdIs.stream().filter(p -> !Objects.equals(p.getUserId(), SecurityUtils.getUserId())).collect(Collectors.toList());
        talentRelateJobFoldersDTO.excludeSelf(SecurityUtils.getUserId());
        RLock lock = redissonClient.getLock(RedisConstants.RELATE_JOB_FOLDER_LOCK_ + folderId);
        try {
            boolean success = lock.tryLock(30, 300, TimeUnit.SECONDS);
            if (!success) {
                throw new BadRequestAlertException("Current folder is editing.");
            }
            List<TalentAssociationJobFolder> deleteList = new ArrayList<>();
            List<RelateJobFolderUserInfo> addOwnerList = new ArrayList<>();
            List<RelateJobFolderUserInfo> addShareList = new ArrayList<>();
            List<TalentAssociationJobFolder> roleChangedFolders = new ArrayList<>();
            allocationChangeList(talentRelateJobFoldersByFolderIdIs, talentRelateJobFoldersDTO.getOwner(), talentRelateJobFoldersDTO.getShare(), deleteList, addOwnerList, addShareList, roleChangedFolders);
            talentRelateJobFolderRepository.saveAllAndFlush(roleChangedFolders);
            talentRelateJobFolderRepository.deleteAll(deleteList);
            List<TalentAssociationJobFolder> addList = getAddTalentAssociationJobFolder(addOwnerList, addShareList, jobId, folderId);
            talentRelateJobFolderRepository.saveAllAndFlush(addList);
            esFillerTalentService.updateTalentRelateJobFolder(SecurityUtils.getTenantId(), jobId, folderId, getUpdateTalentRelateJobFolderBody(exist.getFolderId()));
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return getTalentRelateJobFoldersDTO(folderId, SecurityUtils.getUserId());
    }

    @Override
    @Transactional
    public TalentRelateJobFoldersDTO mergeRelateJobFoldersRequest(String folderId) {
        if (StrUtil.isEmpty(folderId)) {
            throw new CustomParameterizedException("Folder id cannot be empty!");
        }
        TalentAssociationJobFolder merge = talentRelateJobFolderRepository.getTalentRelateJobFolderByFolderIdIsAndUserIdIs(folderId, SecurityUtils.getUserId());
        if (merge == null) {
            throw new CustomParameterizedException("Folder not exist!");
        }
        if (merge.getStatus() == RelateJobFolderStatus.CONFIRM) {
            throw new CustomParameterizedException("Merge Request complete!");
        }
        TalentAssociationJobFolder original = talentRelateJobFolderRepository.getTalentRelateJobFolderByJobIdIsAndUserIdIsAndStatusIs(merge.getJobId(), SecurityUtils.getUserId(), RelateJobFolderStatus.CONFIRM);
        if (original == null) {
            receiveRequest(merge);
        } else {
            //合并操作
            mergeRequest(merge, original);
        }
        return getTalentRelateJobFoldersDTO(folderId, SecurityUtils.getUserId());
    }

    @Override
    @Transactional
    public void rejectRelateJobFoldersRequest(String folderId) {
        if (StrUtil.isEmpty(folderId)) {
            throw new CustomParameterizedException("Folder id cannot be empty!");
        }
        TalentAssociationJobFolder merge = talentRelateJobFolderRepository.getTalentRelateJobFolderByFolderIdIsAndUserIdIs(folderId, SecurityUtils.getUserId());
        if (merge == null) {
            throw new CustomParameterizedException("Request complete!");
        }
        if (merge.getStatus() == RelateJobFolderStatus.CONFIRM) {
            throw new CustomParameterizedException("Request complete!");
        }
        RLock lock = redissonClient.getLock(RedisConstants.RELATE_JOB_FOLDER_LOCK_ + folderId);
        try {
            boolean success = lock.tryLock(30, 300, TimeUnit.SECONDS);
            if (!success) {
                throw new BadRequestAlertException("Current folder is editing.");
            }
            talentRelateJobFolderRepository.delete(merge);
            esFillerTalentService.updateTalentRelateJobFolder(SecurityUtils.getTenantId(), merge.getJobId(), merge.getFolderId(), getUpdateTalentRelateJobFolderBody(merge.getFolderId()));
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    @Transactional
    public void deleteRelateJobFolders(String folderId) {
        if (StrUtil.isEmpty(folderId)) {
            throw new CustomParameterizedException("Folder id cannot be empty!");
        }
        TalentAssociationJobFolder exist = talentRelateJobFolderRepository.getTalentRelateJobFolderByFolderIdIsAndUserIdIs(folderId, SecurityUtils.getUserId());
        if (exist == null) {
            throw new CustomParameterizedException("Folder not exist!");
        }
        if (exist.getStatus() != RelateJobFolderStatus.CONFIRM) {
            throw new CustomParameterizedException("You cannot edit unconfirmed folders!");
        }
        if (exist.getRole() != RelateJobFolderUserRole.OWNER) {
            throw new CustomParameterizedException("You cannot edit folders authority!");
        }
        RLock lock = redissonClient.getLock(RedisConstants.RELATE_JOB_FOLDER_LOCK_ + folderId);
        try {
            boolean success = lock.tryLock(30, 300, TimeUnit.SECONDS);
            if (!success) {
                throw new BadRequestAlertException("Current folder is editing.");
            }
            List<TalentAssociationJobFolder> talentRelateJobFoldersByFolderIdIs = talentRelateJobFolderRepository.getTalentRelateJobFoldersByFolderIdIs(folderId);
            List<TalentAssociationJobFolderTalent> byTalentRelateJobFolderFolderId = talentRelateJobFolderTalentRepository.findByTalentRelateJobFolderFolderIdIs(folderId);
            talentRelateJobFolderRepository.deleteAll(talentRelateJobFoldersByFolderIdIs);
            talentRelateJobFolderTalentRepository.deleteAll(byTalentRelateJobFolderFolderId);
            esFillerTalentService.deleteTalentRelateJobFolder(SecurityUtils.getTenantId(), exist.getJobId(), folderId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    @Transactional
    public AddTalentsToFoldersOutput addTalentsToFolders(String folderId, TalentsToFoldersDTO talentsToFoldersDTO) {
        if (talentsToFoldersDTO.getTalentIds() == null) {
            throw new CustomParameterizedException("Talent id list cannot be empty!");
        }
        if (StrUtil.isEmpty(folderId)) {
            throw new CustomParameterizedException("Folder id cannot be empty!");
        }
        TalentAssociationJobFolder exist = talentRelateJobFolderRepository.getTalentRelateJobFolderByFolderIdIsAndUserIdIs(folderId, SecurityUtils.getUserId());
        if (exist == null) {
            throw new CustomParameterizedException("Folder not exist!");
        }
        if (exist.getStatus() != RelateJobFolderStatus.CONFIRM) {
            throw new CustomParameterizedException("You need confirm merge request!");
        }
        RLock lock = redissonClient.getLock(RedisConstants.RELATE_JOB_FOLDER_LOCK_ + folderId);
        try {
            boolean success = lock.tryLock(30, 300, TimeUnit.SECONDS);
            if (!success) {
                throw new BadRequestAlertException("Current folder is editing.");
            }
            return addTalentsToFolder(folderId, talentsToFoldersDTO.getTalentIds());
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Resource
    private TalentRepository talentRepository;

    private List<SimpleTalent> getSimpleTalent(List<Long> existTalent) {
        List<TalentV3> allByIdIn = talentRepository.findAllByIdIn(existTalent);
        return allByIdIn.stream().map(f -> {
            SimpleTalent simpleTalent = new SimpleTalent();
            simpleTalent.setId(f.getId());
            simpleTalent.setName(CommonUtils.formatFullName(f.getFirstName(), f.getLastName()));
            return simpleTalent;
        }).collect(Collectors.toList());
    }

    private AddTalentsToFoldersOutput addTalentsToFolder(String folderId, List<Long> talentIds) throws Exception {
        StopWatch stopWatch = new StopWatch("addRelateJobFolders");
        stopWatch.start("[3.1] addTalentsToFoldersOutput");
        AddTalentsToFoldersOutput addTalentsToFoldersOutput = new AddTalentsToFoldersOutput();
        List<TalentAssociationJobFolderTalent> talentAssociationJobFolder = talentRelateJobFolderTalentRepository.findByTalentRelateJobFolderFolderIdIsAndTalentIdIn(folderId, talentIds);
        List<Long> existTalent = talentAssociationJobFolder.stream().map(TalentAssociationJobFolderTalent::getTalentId).collect(Collectors.toList());
        addTalentsToFoldersOutput.setExist(getSimpleTalent(existTalent));
        talentIds.removeAll(existTalent);
        addTalentsToFoldersOutput.setSuccess(getSimpleTalent(talentIds));

        stopWatch.stop();
        stopWatch.start("[3.2] addTalentsToFoldersSave");
        List<TalentAssociationJobFolderTalent> addList = new ArrayList<>();
        for (Long talentId : talentIds) {
            TalentAssociationJobFolderTalent talent = new TalentAssociationJobFolderTalent();
            talent.setTalentRelateJobFolderFolderId(folderId);
            talent.setTalentId(talentId);
            talent.setAddedBy(SecurityUtils.getUserId());
            addList.add(talent);
        }
        talentRelateJobFolderTalentRepository.saveAllAndFlush(addList);
        if (!talentIds.isEmpty()) {
            stopWatch.stop();
            stopWatch.start("[3.3] updateTalentToFoldersOfPreSubmitTalents");
            esFillerTalentService.updateTalentToFoldersOfPreSubmitTalents(SecurityUtils.getTenantId(), getAddTalentsToFolder(folderId, talentIds));
        }
        stopWatch.stop();
        log.info("[apn @{}] addRelateJobFoldersMethod time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return addTalentsToFoldersOutput;
    }

    private String getAddTalentsToFolder(String folderId, List<Long> talentIds) {
        List<TalentAssociationJobFolder> folder = talentRelateJobFolderRepository.getTalentRelateJobFoldersByFolderIdIs(folderId);
        JSONObject body = new JSONObject();
        body.put("jobId", folder.get(0).getJobId());
        body.put("folderId", folderId);
        List<String> sTalentIds = talentIds.stream().map(String::valueOf).collect(Collectors.toList());
        body.put("talentIds", sTalentIds);
        body.put("talentFolderResponsibility", getTalentFolderResponsibility());
        return body.toString();
    }

    private Object getTalentFolderResponsibility() {
        List<EnumUserResponsibility> userResponsibilities = enumCommonService.findFoldersOfPreSubmitTalentsEnumUserResponsibility();
        JSONObject jsonObject = new JSONObject();
        for (EnumUserResponsibility userResponsibility : userResponsibilities) {
            String esKey = getResponsibilityEsKey(userResponsibility.getFoldersOfPreSubmitTalentsEsKey());
            switch (userResponsibility.getLabel()) {
                case ResponsibilityConstants.ADDED_BY:
                    jsonObject.put(esKey, packageIdListToIdAndNameJSONArray(List.of(SecurityUtils.getUserId())));
                    break;
            }
        }

        return jsonObject;
    }

    @Override
    @Transactional
    public void removeTalentsToFolders(String folderId, TalentsToFoldersDTO talentsToFoldersDTO) {
        if (talentsToFoldersDTO.getTalentIds() == null) {
            throw new CustomParameterizedException("Talent id list cannot be empty!");
        }
        if (StrUtil.isEmpty(folderId)) {
            throw new CustomParameterizedException("Folder id cannot be empty!");
        }
        TalentAssociationJobFolder exist = talentRelateJobFolderRepository.getTalentRelateJobFolderByFolderIdIsAndUserIdIs(folderId, SecurityUtils.getUserId());
        if (exist == null) {
            throw new CustomParameterizedException("Folder not exist!");
        }
        if (exist.getStatus() != RelateJobFolderStatus.CONFIRM) {
            throw new CustomParameterizedException("You need confirm merge request!");
        }
        RLock lock = redissonClient.getLock(RedisConstants.RELATE_JOB_FOLDER_LOCK_ + folderId);
        try {
            boolean success = lock.tryLock(30, 300, TimeUnit.SECONDS);
            if (!success) {
                throw new BadRequestAlertException("Current folder is editing.");
            }
            List<TalentAssociationJobFolderTalent> talentAssociationJobFolder = talentRelateJobFolderTalentRepository.findByTalentRelateJobFolderFolderIdIsAndTalentIdIn(folderId, talentsToFoldersDTO.getTalentIds());
            if (talentAssociationJobFolder.size() != talentsToFoldersDTO.getTalentIds().size()) {
                throw new CustomParameterizedException("Some candidates have been removed to the folder!");
            }
            talentRelateJobFolderTalentRepository.deleteAll(talentAssociationJobFolder);
            esFillerTalentService.deleteTalentToFoldersOfPreSubmitTalents(SecurityUtils.getTenantId(), getDeleteTalentsRequestBody(exist, talentsToFoldersDTO.getTalentIds()));
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private String getDeleteTalentsRequestBody(TalentAssociationJobFolder exist, List<Long> talentIds) {
        JSONObject body = new JSONObject();
        body.put("jobId", String.valueOf(exist.getJobId()));
        body.put("folderId", exist.getFolderId());
        List<String> strTalentIds = talentIds.stream().map(String::valueOf).collect(Collectors.toList());
        body.put("talentIdsToRemove", strTalentIds);
        return body.toString();
    }

    @Override
    public TalentRelateJobFoldersDTO getRelateJobFolders(String folderId) {
        if (StrUtil.isEmpty(folderId)) {
            throw new CustomParameterizedException("Folder id cannot be empty!");
        }
        TalentAssociationJobFolder exist = talentRelateJobFolderRepository.getTalentRelateJobFolderByFolderIdIsAndUserIdIs(folderId, SecurityUtils.getUserId());
        if (exist == null) {
            throw new CustomParameterizedException("Folder not exist!");
        }
        return getTalentRelateJobFoldersDTO(folderId, SecurityUtils.getUserId());
    }

    private void mergeRequest(TalentAssociationJobFolder merge, TalentAssociationJobFolder original) {
        String mergeFolderId = merge.getFolderId();
        String originalFolderId = original.getFolderId();
        RLock lock = redissonClient.getLock(RedisConstants.RELATE_JOB_FOLDER_LOCK_ + mergeFolderId);
        RLock OriLock = redissonClient.getLock(RedisConstants.RELATE_JOB_FOLDER_LOCK_ + originalFolderId);
        try {
            boolean success = lock.tryLock(30, 300, TimeUnit.SECONDS);
            if (!success) {
                throw new BadRequestAlertException("Current folder is editing.");
            }
            success = OriLock.tryLock(30, 300, TimeUnit.SECONDS);
            if (!success) {
                throw new BadRequestAlertException("Current folder is editing.");
            }
            talentRelateJobFolderRepository.delete(merge);
            Long mergeUserId = merge.getUserId();
            RelateJobFolderUserRole mergeRole = merge.getRole();
            List<TalentAssociationJobFolder> talentRelateJobFoldersList = talentRelateJobFolderRepository.getTalentRelateJobFoldersByFolderIdIs(originalFolderId);
            List<TalentAssociationJobFolder> pendingDelete = talentRelateJobFoldersList.stream().filter(f -> RelateJobFolderStatus.PENDING.equals(f.getStatus())).collect(Collectors.toList());
            talentRelateJobFoldersList.removeAll(pendingDelete);
            talentRelateJobFoldersList.forEach(c -> {
                c.setFolderId(mergeFolderId);
                if (mergeUserId.equals(c.getUserId())) {
                    c.setRole(mergeRole);
                }
            });
            talentRelateJobFolderRepository.saveAllAndFlush(talentRelateJobFoldersList);
            talentRelateJobFolderRepository.deleteAll(pendingDelete);
            List<TalentAssociationJobFolderTalent> changeFolderPoint = talentRelateJobFolderTalentRepository.findByTalentRelateJobFolderFolderIdIs(originalFolderId);
            changeFolderPoint.forEach(a -> a.setTalentRelateJobFolderFolderId(mergeFolderId));
            talentRelateJobFolderTalentRepository.saveAllAndFlush(changeFolderPoint);
            esFillerTalentService.mergeTalentRelateJobFolder(SecurityUtils.getTenantId(), original.getJobId(), originalFolderId, getMergeRequestBody(mergeFolderId));
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
            if (OriLock.isHeldByCurrentThread()) {
                OriLock.unlock();
            }
        }
    }

    private String getMergeRequestBody(String mergeToFolderId) {
        List<TalentAssociationJobFolder> folder = talentRelateJobFolderRepository.getTalentRelateJobFoldersByFolderIdIs(mergeToFolderId);
        Instant minCreateTime = getMinCreateTime(folder);

        JSONObject body = new JSONObject();
        body.put("folderCreatedDate", DateUtil.fromInstantToUtcDateTime(minCreateTime));
        body.put("jobFolderResponsibility", getJobFolderResponsibility(folder));
        body.put("mergeTo", mergeToFolderId);
        return body.toString();
    }

    private void receiveRequest(TalentAssociationJobFolder merge) {
        RLock lock = redissonClient.getLock(RedisConstants.RELATE_JOB_FOLDER_LOCK_ + merge.getFolderId());
        try {
            boolean success = lock.tryLock(30, 300, TimeUnit.SECONDS);
            if (!success) {
                throw new BadRequestAlertException("Current folder is editing.");
            }
            merge.setStatus(RelateJobFolderStatus.CONFIRM);
            talentRelateJobFolderRepository.saveAndFlush(merge);
            esFillerTalentService.updateTalentRelateJobFolder(SecurityUtils.getTenantId(), merge.getJobId(), merge.getFolderId(), getUpdateTalentRelateJobFolderBody(merge.getFolderId()));
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private List<TalentAssociationJobFolder> getAddTalentAssociationJobFolder(List<RelateJobFolderUserInfo> addOwnerList, List<RelateJobFolderUserInfo> addShareList, Long jobId, String folderId) {
        List<TalentAssociationJobFolder> ret = new ArrayList<>();
        if (addOwnerList != null) {
            for (RelateJobFolderUserInfo userInfoDTO : addOwnerList) {
                TalentAssociationJobFolder folder = new TalentAssociationJobFolder();
                folder.setJobId(jobId);
                folder.setFolderId(folderId);
                folder.setUserId(userInfoDTO.getId());
                folder.setRole(RelateJobFolderUserRole.OWNER);
                folder.setStatus(RelateJobFolderStatus.PENDING);
                ret.add(folder);
            }
        }
        if (addShareList != null) {
            for (RelateJobFolderUserInfo userInfoDTO : addShareList) {
                TalentAssociationJobFolder folder = new TalentAssociationJobFolder();
                folder.setJobId(jobId);
                folder.setFolderId(folderId);
                folder.setUserId(userInfoDTO.getId());
                folder.setRole(RelateJobFolderUserRole.SHARER);
                folder.setStatus(RelateJobFolderStatus.PENDING);
                ret.add(folder);
            }
        }
        return ret;
    }

    private void allocationChangeList(List<TalentAssociationJobFolder> talentRelateJobFoldersByFolderIdIs, List<RelateJobFolderUserInfo> owner, List<RelateJobFolderUserInfo> share,
                                      List<TalentAssociationJobFolder> deleteList, List<RelateJobFolderUserInfo> addOwnerList, List<RelateJobFolderUserInfo> addShareList, List<TalentAssociationJobFolder> roleChangedFolders) {
        // 创建一个HashSet来存储所有的userId
        Set<Long> userIds = talentRelateJobFoldersByFolderIdIs.stream().map(TalentAssociationJobFolder::getUserId).collect(Collectors.toSet());

        // 找出在owner和share中不存在的TalentAssociationJobFolder
        for (TalentAssociationJobFolder folder : talentRelateJobFoldersByFolderIdIs) {
            if (owner.stream().noneMatch(o -> Objects.equals(o.getId(), folder.getUserId())) &&
                    share.stream().noneMatch(s -> Objects.equals(s.getId(), folder.getUserId()))) {
                deleteList.add(folder);
            }
        }

        // 遍历TalentAssociationJobFolder列表
        for (TalentAssociationJobFolder folder : talentRelateJobFoldersByFolderIdIs) {
            Long userId = folder.getUserId();
            // 检查folder的角色是否为OWNER，但在owner列表中不存在且在share存在
            // 检查folder的角色是否为SHARER，但在share列表中不存在且在owner存在
            if (folder.getRole() == RelateJobFolderUserRole.OWNER &&
                    owner.stream().noneMatch(o -> o.getId().equals(userId)) &&
                    share.stream().anyMatch(o -> o.getId().equals(userId))) {
                folder.setRole(RelateJobFolderUserRole.SHARER);
                roleChangedFolders.add(folder);
            } else if (folder.getRole() == RelateJobFolderUserRole.SHARER &&
                    share.stream().noneMatch(s -> s.getId().equals(userId)) &&
                    owner.stream().anyMatch(s -> s.getId().equals(userId))) {
                folder.setRole(RelateJobFolderUserRole.OWNER);
                roleChangedFolders.add(folder);
            }
        }

        // 找出在TalentAssociationJobFolder中不存在的owner和share
        for (RelateJobFolderUserInfo user : owner) {
            if (!userIds.contains(user.getId())) {
                addOwnerList.add(user);
            }
        }
        for (RelateJobFolderUserInfo user : share) {
            if (!userIds.contains(user.getId())) {
                addShareList.add(user);
            }
        }
    }

    private TalentRelateJobFoldersDTO getTalentRelateJobFoldersDTO(String folderId, Long userId) {
        TalentRelateJobFoldersDTO ret = new TalentRelateJobFoldersDTO();
        TalentAssociationJobFolder talentAssociationJobFolder = talentRelateJobFolderRepository.getTalentRelateJobFolderByFolderIdIsAndUserIdIs(folderId, userId);
        if (talentAssociationJobFolder == null) {
            return ret;
        }
        Long jobId = talentAssociationJobFolder.getJobId();
        RelateJobFolderJobInfoVM talentRelateJobFolders = jobRepository.findTalentRelateJobFolders(jobId);
        if (talentRelateJobFolders == null) {
            return ret;
        }
        Optional<JobSharingPlatformTalentRelatedJobFolderRelation> relatedFolderRelationOpt = jobSharingPlatformRelatedFolderRepository.findByTalentAssociatedJobFolderFolderId(talentAssociationJobFolder.getFolderId());
        if (relatedFolderRelationOpt.isPresent()) {
            ret.setSharedLinkExpireTime(relatedFolderRelationOpt.get().getSharedLinkExpireTime());
        }
        ret.setId(folderId);
        ret.setCreateBy(getCreateBy(talentAssociationJobFolder));
        setFolderType(ret, jobId, talentAssociationJobFolder);
        setFolderNote(ret, jobId);
        setFolderBaseInfo(ret, talentRelateJobFolders);
        setFolderUserRoleInfo(ret, talentRelateJobFolderRepository.getTalentRelateJobFoldersByFolderIdIs(folderId));
        return ret;
    }

    private void setFolderType(TalentRelateJobFoldersDTO ret, Long jobId, TalentAssociationJobFolder talentAssociationJobFolder) {
        boolean permission = judgeJobPermission(jobId, SecurityUtils.getUserId());
        boolean isOwner = RelateJobFolderUserRole.OWNER == talentAssociationJobFolder.getRole();
        boolean isPending = RelateJobFolderStatus.PENDING == talentAssociationJobFolder.getStatus();
        TalentAssociationJobFolder exist = talentRelateJobFolderRepository.getTalentRelateJobFolderByJobIdIsAndUserIdIsAndStatusIs(jobId, SecurityUtils.getUserId(), RelateJobFolderStatus.CONFIRM);
        if (!permission) {
            ret.setType(RelateJobFolderType.NO_AUTHORITY.name());
        } else if (exist != null && isPending) {
            ret.setType(RelateJobFolderType.PENDING_MERGE.name());
        } else if (exist == null && isPending) {
            ret.setType(RelateJobFolderType.PENDING_RECEIVE.name());
        } else if (isOwner) {
            ret.setType(RelateJobFolderType.FOLDER_OWNER.name());
        } else {
            ret.setType(RelateJobFolderType.NORMAL.name());
        }
    }

    private boolean judgeJobPermission(Long jobId, Long userId) {
        ResponseEntity<PermissionUserTeamPermissionVM> res = userService.getDataPermissionsByUserId(userId);
        if (res == null) {
            throw new ExternalInterfaceException("user service getDataPermissionsByUserId error", 500);
        }
        PermissionUserTeamPermissionVM.PermissionDetail userPermission = res.getBody().getJobPermission();
        if (userPermission == null) {
            throw new ExternalInterfaceException("user service getDataPermissionsByUserId error", 500);
        }

        Optional<JobV3> job = jobRepository.findById(jobId);
        if (job.isEmpty()) {
            return false;
        }
        JobV3 jobV3 = job.get();
        DataScope dataScope = DataScope.fromDbValue(userPermission.getDataScope());
        Set<Long> teamIds = userPermission.getTeamIds() != null ? userPermission.getTeamIds() : new HashSet<>();
        return switch (dataScope) {
            case PERMISSION_ALL -> true;
            case PERMISSION_EXTRA_TEAM, PERMISSION_TEAM ->
                    Objects.equals(userPermission.getPrimaryTeamId(), jobV3.getPermissionTeamId()) || teamIds.contains(jobV3.getPermissionTeamId());
            case PERMISSION_SELF -> Objects.equals(userId, jobV3.getPermissionUserId());
            case PERMISSION_NO -> false;
        };
    }


    private RelateJobFolderUserInfo getCreateBy(TalentAssociationJobFolder talentAssociationJobFolder) {
        String userId = getUserIdFromCreatedBy(talentAssociationJobFolder.getCreatedBy());
        if (!NumberUtil.isNumber(userId)) {
            return null;
        }
        Long lUserId = Long.valueOf(userId);
        ResponseEntity<User> res = userService.getUserById(lUserId);
        if (res != null) {
            User body = res.getBody();
            if (body != null) {
                RelateJobFolderUserInfo userDTO = new RelateJobFolderUserInfo();
                userDTO.setId(body.getId());
                userDTO.setFullName(CommonUtils.formatFullName(body.getFirstName(), body.getLastName()));
                return userDTO;
            }
        }
        return null;
    }

    private RelateJobFolderUserInfo getCreateBy(List<TalentAssociationJobFolder> talentAssociationJobFolderList) {
        TalentAssociationJobFolder earlyFolder = talentAssociationJobFolderList.stream()
                .min(Comparator.comparing(TalentAssociationJobFolder::getCreatedDate))
                .orElse(null);
        if (earlyFolder == null) {
            return null;
        }
        String userId = getUserIdFromCreatedBy(earlyFolder.getCreatedBy());
        if (!NumberUtil.isNumber(userId)) {
            return null;
        }
        Long lUserId = Long.valueOf(userId);
        ResponseEntity<User> res = userService.getUserById(lUserId);
        if (res != null) {
            User body = res.getBody();
            if (body != null) {
                RelateJobFolderUserInfo userDTO = new RelateJobFolderUserInfo();
                userDTO.setId(body.getId());
                userDTO.setFullName(body.getFirstName() + " " + body.getLastName());
                return userDTO;
            }
        }
        return null;
    }

    private String getUserIdFromCreatedBy(String createdBy) {
        if (StringUtils.isNotEmpty(createdBy)) {
            String[] idStrings = createdBy.split(",");
            if (idStrings.length == 2) {
                return idStrings[0];
            } else {
                return createdBy;
            }
        }
        return "unknown";
    }

    private void setFolderNote(TalentRelateJobFoldersDTO ret, Long jobId) {
        List<JobNote> noteList = jobNoteRepository.findByJobIdAndVisibleOrderByCreatedDateDesc(jobId, true);
        if (noteList != null && !noteList.isEmpty()) {
            ret.setNote(noteList.get(0).getNote());
        }
    }

    @Resource
    private UserService userService;

    private void setFolderUserRoleInfo(TalentRelateJobFoldersDTO ret, List<TalentAssociationJobFolder> talentAssociationJobFolderList) {
        List<Long> userIdList = talentAssociationJobFolderList.stream().map(TalentAssociationJobFolder::getUserId).collect(Collectors.toList());
        List<User> responseFindByIds = userService.findByIds(userIdList).getBody();
        Map<Long, User> userMap = new HashMap<>();
        if (responseFindByIds != null) {
            userMap = responseFindByIds.stream()
                    .collect(Collectors.toMap(User::getId, user -> user));
        }

        for (TalentAssociationJobFolder folder : talentAssociationJobFolderList) {
            switch (folder.getRole()) {
                case OWNER:
                    List<RelateJobFolderUserInfo> owner = ret.getOwner();
                    if (owner == null) {
                        owner = new ArrayList<>();
                    }
                    RelateJobFolderUserInfo userDTO = new RelateJobFolderUserInfo();
                    if (userMap.containsKey(folder.getUserId())) {
                        userDTO.setId(folder.getUserId());
                        userDTO.setFullName(userMap.get(folder.getUserId()).getFirstName() + " " + userMap.get(folder.getUserId()).getLastName());
                        userDTO.setType(getFolderUserType(folder));
                    }
                    owner.add(userDTO);
                    ret.setOwner(owner);
                    break;
                case SHARER:
                    List<RelateJobFolderUserInfo> share = ret.getShare();
                    if (share == null) {
                        share = new ArrayList<>();
                    }
                    RelateJobFolderUserInfo shareUser = new RelateJobFolderUserInfo();
                    if (userMap.containsKey(folder.getUserId())) {
                        shareUser.setId(folder.getUserId());
                        shareUser.setFullName(userMap.get(folder.getUserId()).getFirstName() + " " + userMap.get(folder.getUserId()).getLastName());
                        shareUser.setType(getFolderUserType(folder));
                    }
                    share.add(shareUser);
                    ret.setShare(share);
                    break;
            }
        }
    }

    private String getFolderUserType(TalentAssociationJobFolder folder) {
        boolean permission = judgeJobPermission(folder.getJobId(), folder.getUserId());
        if (!permission) {
            return RelateJobFolderUserType.NO_AUTHORITY.name();
        } else if (folder.getStatus().equals(RelateJobFolderStatus.PENDING)) {
            return RelateJobFolderUserType.PENDING.name();
        } else {
            return RelateJobFolderUserType.RECEIVE.name();
        }
    }

    private void setFolderBaseInfo(TalentRelateJobFoldersDTO ret, RelateJobFolderJobInfoVM talentRelateJobFolders) {
        ret.setJobId(talentRelateJobFolders.getJobId());
        ret.setCompanyName(talentRelateJobFolders.getCompanyName());
        ret.setTitle(talentRelateJobFolders.getTitle());
        ret.setStatus(JobStatus.fromDbValue(talentRelateJobFolders.getStatus()));
        ret.setPriority(talentRelateJobFolders.getPriority());
    }

    @Override
    public Boolean isFolderIdValid(String folderId) {
        List<TalentAssociationJobFolder> folders = talentRelateJobFolderRepository.findAllByFolderId(folderId);
        return !folders.isEmpty();
    }

    @Override
    public TalentRelateJobFolderBasicInfoDTO getTalentRelateJobFolderBasicInfo(TalentRelateJobFolderSearchDTO talentRelateJobFolderSearchDTO) {
        List<TalentAssociationJobFolder> talentAssociationJobFolderList = talentRelateJobFolderRepository.findAllByUserIdAndJobId(talentRelateJobFolderSearchDTO.getUserId(), talentRelateJobFolderSearchDTO.getJobId());
        var basicInfoDTO = new TalentRelateJobFolderBasicInfoDTO();

        if (talentAssociationJobFolderList.isEmpty()) {
            return basicInfoDTO;
        }

        if(talentAssociationJobFolderList.size() > 1){
            log.warn("[APN Talent] Talent Relate Job Folder: Folders under one job and one user are more than one");
        }
        var user = userService.getUserById(talentRelateJobFolderSearchDTO.getUserId()).getBody();
        //should be only one entity
        var talentAssociationJobFolder = talentAssociationJobFolderList.get(0);

        basicInfoDTO.setUserId(talentAssociationJobFolder.getUserId());
        basicInfoDTO.setJobId(talentAssociationJobFolder.getJobId());
        basicInfoDTO.setTenantId(user.getTenantId());

        return basicInfoDTO;
    }
}

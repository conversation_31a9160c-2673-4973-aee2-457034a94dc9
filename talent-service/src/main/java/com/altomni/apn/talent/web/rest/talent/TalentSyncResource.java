package com.altomni.apn.talent.web.rest.talent;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.constants.Constants;
import com.altomni.apn.talent.service.talent.TalentSyncService;
import com.altomni.apn.talent.web.rest.vm.MqMessageCountVM;
import com.altomni.apn.talent.web.rest.vm.TalentSyncToMqVM;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.ws.rs.GET;
import java.net.URISyntaxException;
import java.util.Collection;

/**
 * REST controller for managing Talent.
 */
@Api(tags = {"Talent"})
@RestController
@RequestMapping("/api/v3")
public class TalentSyncResource {

    private final Logger log = LoggerFactory.getLogger(TalentSyncResource.class);

    private static final String ENTITY_NAME = "talent";

    private static final String PIN = "IPG888hq";

    @Resource
    private TalentSyncService talentSyncService;


    @PostMapping("/xxl-job/sync-failed-talents-to-es")
    public ResponseEntity<String> syncFailedTalentsToEs(HttpServletRequest request) {
        if (!request.getHeader(Constants.EMAILY_XXL_JOB_TOKEN).equals(PIN)) {
            log.error("[XXL-JOB] xxl job request token is wrong!");
            throw new CustomParameterizedException("APN xxl-job token is wrong!");
        }

        String res = talentSyncService.syncFailedTalentsToEs();
        return ResponseEntity.ok(res);
    }

    @PostMapping("/xxl-job/sync-talents-to-es")
    public ResponseEntity<Void> syncTalentsToEs() {
        log.info("[XXL-JOB] syncTalentsToEsXxlJob!");
        talentSyncService.syncTalentsToEs();
        return ResponseEntity.ok().build();
    }

    /**
     * 定期扫描所有APN的Manager级别以及以上的候选人profile，和LinkedIn对应的profile进行比对，如果有公司上的变动，第一时间发出提醒。
     * 定义人选职位title:
     *  最近1-2份工作的职位title内包含关键字比如:
     *      manager, lead, Director, president, vp, svp, CEO, CFO, COO, CTO, CMO, CXO,
     *      founder, cofounder, co-founder, HRD, HR Head, HR Generalist, Talent acquisition partner, TA Head, TA Lead,
     *      Recruitment Lead, Head of XXXX, Senior Principal, sr. principal, fellow, principal
     *
     */
    @PostMapping("/xxl-job/scan-talents-with-manager-title")
    public ResponseEntity<Void> scanTalentsWithManagerTitle() {
        log.info("[XXL-JOB] scanTalentsWithManagerTitle!");
        talentSyncService.scanTalentsWithManagerTitle();
        return ResponseEntity.accepted().build();
    }

//    @PostMapping("/{talentId}/social-profile/search-profile")
//    @NoRepeatSubmit
//    public ResponseEntity<String> sendSocialProfileFetchProfileRequest(@Valid @RequestBody SocialProfileProfileContentRequestDTO socialProfileProfileContentRequestDTO) throws URISyntaxException {
//        log.info("[APN: Contact Profile @{}] REST request to search social profile content: {}", SecurityUtils.getUserId(), socialProfileProfileContentRequestDTO);
//
//        HttpStatus status = socialProfileService.processFetchSocialProfileContent(socialProfileProfileContentRequestDTO);
//        return new ResponseEntity<>(status);
//    }


    @PostMapping("/canal/check-talent-mq-message-count")
    public ResponseEntity<MqMessageCountVM> checkTalentMqMessageCount() {
        log.debug("[Canal] checkTalentMqMessageCount!");
        return ResponseEntity.ok(talentSyncService.checkTalentMqMessageCount());
    }

    @PostMapping("/canal/check-talent-hr-mq-message-count")
    public ResponseEntity<MqMessageCountVM> checkTalentHrMqMessageCount() {
        log.debug("[Canal] checkTalentHrMqMessageCount!");
        return ResponseEntity.ok(talentSyncService.checkTalentHrMqMessageCount());
    }

    @PostMapping("/canal/sync-talents-to-mq")
    public ResponseEntity<Void> syncTalentsToMQ(@RequestBody TalentSyncToMqVM talentSyncToMqVM) {
        log.info("[EsFillerTalentService: syncTalentToMQ @{}] syncTalentsToMQ: {}", SecurityUtils.getUserId(), talentSyncToMqVM);
        talentSyncService.syncTalentsToMQ(talentSyncToMqVM.getTalentIds(), talentSyncToMqVM.getPriority());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/canal/bulk-sync-talents-to-mq")
    public ResponseEntity<Void> bulkSyncTalentsToMQ(@RequestBody TalentSyncToMqVM talentSyncToMqVM) {
        log.info("[EsFillerTalentService: syncTalentToMQ @{}] syncTalentsToMQ: {}", SecurityUtils.getUserId(), talentSyncToMqVM);
        talentSyncService.bulkSyncTalentsToMQ(talentSyncToMqVM.getTalentIds(), talentSyncToMqVM.getPriority());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/canal/sync-talents-to-hr-mq")
    public ResponseEntity<Void> syncTalentsToHrMQ(@RequestBody TalentSyncToMqVM talentSyncToMqVM) {
        log.info("[EsFillerTalentService: syncTalentsToHrMQ @{}] syncTalentsToHrMQ: {}", SecurityUtils.getUserId(), talentSyncToMqVM);
        talentSyncService.syncTalentsToHrMQ(talentSyncToMqVM.getTalentIds(), talentSyncToMqVM.getPriority());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/user-change-team/user/{userId}/sync-talents-to-mq")
    public ResponseEntity<Void> syncTalentsToMQByOwner(@PathVariable("userId") Long userId) {
        log.info("[EsFillerTalentService: syncTalentToMQ by owner @{}] userId: {}", SecurityUtils.getUserId(), userId);
        talentSyncService.syncTalentsToMQByOwner(userId);
        return ResponseEntity.ok().build();
    }
}

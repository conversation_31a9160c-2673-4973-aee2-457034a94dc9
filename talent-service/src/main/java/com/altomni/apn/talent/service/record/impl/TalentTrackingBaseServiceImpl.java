package com.altomni.apn.talent.service.record.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.SqlUtil;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.service.record.TalentTrackingBaseService;
import org.hibernate.query.internal.NativeQueryImpl;
import org.hibernate.transform.Transformers;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service("reportBaseService")
public class TalentTrackingBaseServiceImpl implements TalentTrackingBaseService {

    static final String AND_SYMBOL = " AND ";

    static final String ALL_SELECT = "all";

    @PersistenceContext
    EntityManager entityManager;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    @Override
    public <T> List<T> searchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        Integer key = checkInList(map);
        if (key == null) {
            return doSearchData(queryStr, clazz, map);
        } else {
            return doPartitionSearchData(key, queryStr, clazz, map);
        }
    }

    private <T> List<T> doPartitionSearchData(Integer key, String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
            map.put(key, values);
            return searchData(queryStr, clazz, map);
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    public <T> List<T> doSearchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr, clazz);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        return query.getResultList();
    }

    private Integer checkInList(Map<Integer, Object> map) {
        List<Integer> keyList = map.keySet().stream().filter(k -> (map.get(k) instanceof Collection && ((Collection<?>) map.get(k)).size() > SqlUtil.PARTITION_COUNT_999)).collect(Collectors.toList());
        if (CollUtil.isEmpty(keyList)) {
            return null;
        }
        if (keyList.size() > 1) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.RECORD_CHECKINLIST_CONDITIONLIMIT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        return keyList.get(0);
    }

    public List<Map<String, Object>> searchDataWithMap(String queryStr, Map<Integer, Object> map) {
        Integer key = checkInList(map);
        if (key == null) {
            return doSearchDataWithMap(queryStr, map);
        } else {
            return doPartitionSearchDataWithMap(key, queryStr, map);
        }
    }

    private List<Map<String, Object>> doPartitionSearchDataWithMap(Integer key, String queryStr, Map<Integer, Object> map) {
        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
            map.put(key, values);
            return doSearchDataWithMap(queryStr, map);
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    public List<Map<String, Object>> doSearchDataWithMap(String queryStr, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        query.unwrap(NativeQueryImpl.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return query.getResultList();
    }

}

package com.altomni.apn.report.repository.v2;

import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.SQLDialect;
import org.jooq.Table;
import org.jooq.conf.RenderKeywordCase;
import org.jooq.conf.RenderQuotedNames;
import org.jooq.conf.Settings;

import static org.jooq.impl.DSL.*;

public class Constants {

    public static final DSLContext dsl = using(SQLDialect.MYSQL, new Settings()
            // 使用大写关键字
            .withRenderKeywordCase(RenderKeywordCase.UPPER)
            // 格式化 SQL
            .withRenderFormatted(true)
            // 不使用双引号
            .withRenderQuotedNames(RenderQuotedNames.NEVER));

    public static final Table<?> VIEW_APPLICATION_API = table("mv_application_kpi_v2").as("application_kpi");
    public static final Table<?> VIEW_NOTES_KPI = table("mv_note_kpi_v2").as("note_kpi");
    public static final Table<?> VIEW_CREATED__KPI = table("mv_created_kpi_v2").as("created_kpi");

    public static final Field<Long> ID = field("id", Long.class);
    public static final String DATE_DIM_ALIAS = "group_by_date";
    public static final Field<Long> USER_ID = field("user_id", Long.class);
    public static final Field<Boolean> USER_ACTIVATED = field("user_activated", Boolean.class);
    public static final Field<String> USER_NAME = field("user_name", String.class);
    public static final Field<Long> JOB_PTEAM_ID = field("job_pteam_id", Long.class);
    public static final Field<Long> JOB_ID = field("job_id", Long.class);
    public static final Field<Long> ALIAS_JOB_ID = field("application_kpi.job_id", Long.class);
    public static final Field<String> JOB_NAME = field("job_title", String.class);
    public static final Field<Long> COMPANY_ID = field("company_id", Long.class);
    public static final Field<Long> ALIAS_COMPANY_ID = field("application_kpi.company_id", Long.class);
    public static final Field<String> COMPANY_NAME = field("full_business_name", String.class);
    public static final Field<String> COMPANY_NAME_AS = field("full_business_name as companyName", String.class);
    public static final Field<Long> TEAM_ID = field("team_id", Long.class);
    public static final Field<String> TEAM_NAME = field("team_name", String.class);
    public static final Field<Long> TEAM_PARENT_ID = field("team_parent_id", Long.class);
    public static final Field<Integer> TEAM_LEVEL = field("team_level", Integer.class);
    public static final Field<Boolean> TEAM_IS_LEAF = field("team_is_leaf", Boolean.class);
    public static final Field<Long> TENANT_ID = field("tenant_id", Long.class);
    public static final Field<Long> ALIAS_TENANT_ID = field("application_kpi.tenant_id", Long.class);
    public static final Field<String> EVENT_DATE = field("event_date", String.class);
    public static final Field<String> ADD_DATE = field("add_date", String.class);

    public static final Table<?> USER = table("user").as("u");
    public static final Table<?> JOB = table("job").as("j");
    public static final Table<?> COMPANY = table("company").as("c");
    public static final Table<?> COMPANY_USER_RELATION = table("company_user_relation").as("ul");


}

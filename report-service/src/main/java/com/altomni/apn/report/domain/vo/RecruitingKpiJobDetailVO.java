package com.altomni.apn.report.domain.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatusConverter;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.job.JobTypeConverter;
import com.altomni.apn.common.utils.CommonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiJobDetailVO implements Serializable {

    @Id
    private Long jobId;

    private String jobTitle;

    private Long pteamId;

    private String jobCode;

    private Instant postingDate;

    private Instant openDate;

    private Instant tenantWebsitePostingDate;

    @Convert(converter = JobStatusConverter.class)
    private JobStatus jobStatus;

    private Long openings;

    //模板id
    private Long recruitmentProcessId;

    private String jobTypeName;

    @Convert(converter = JobTypeConverter.class)
    private JobType jobType;

    private String companyName;

    private Long companyId;

    private String division;

    private String clientContact;

    private String createdBy;

    private String assignedUser;

    private String minimumPayRate;

    private String maximumPayRate;

    private String minimumBillRate;

    private String maximumBillRate;

    private String ratePer;

    private String startDate;

    private String endDate;

    private String jobLocation;

    private Boolean flexibleLocation;

    private String skills;

    private Long submitToClientNum = 0L;

    private Long firstInterviewNum = 0L;

    private Long secondInterviewNum = 0L;

    private Long finalInterviewNum = 0L;

    private Long interviewNum = 0L;

    private Long offerNum = 0L;

    private Long onboardNum = 0L;

    private Long currency;

    private String jobCurrency;

    private String ecName;

    private Long contractNotes;

    private String jobCooperationStatus;

    private Integer contractDuration;

    @Transient
    private boolean isPrivateJob;

    public String getAssignedUser() {
        if (StrUtil.isBlank(assignedUser)) {
            return "";
        }
        return Arrays.stream(assignedUser.split(","))
                .map(StrUtil::trim)
                .filter(StrUtil::isNotBlank)
                .filter(client -> client.split("-").length > 1)
                .map(client -> {
                    List<String> nameList = StrUtil.split(client, '-', 2);
                    String name = nameList.get(1);
                    String[] names = Arrays.stream(name.split(" ")).filter(StrUtil::isNotBlank).toArray(String[]::new);
                    return nameList.get(0) + "-" + CommonUtils.formatFullName(names[0], names[1]);
                }).collect(Collectors.joining(","));
    }

    public String getSkills() {
        if (StrUtil.isNotBlank(skills)) {
            JSONArray requiredSkillsArray = JSONUtil.parseArray(skills);
            if (CollUtil.isNotEmpty(requiredSkillsArray)) {
                return requiredSkillsArray.stream()
                        .map(StrUtil::toString)
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.joining(","));
            }
        }
        return "";
    }

    public String getJobLocation() {
        return  flexibleLocation != null && flexibleLocation? (StrUtil.isBlank(jobLocation)? "remote": "(remote)" + jobLocation): jobLocation;
    }

    public String getStartDate() {
        if (StrUtil.isNotBlank(startDate)) {
            return startDate.split(" ")[0];
        }
        return startDate;
    }

    public String getEndDate() {
        if (StrUtil.isNotBlank(endDate)) {
            return endDate.split(" ")[0];
        }
        return endDate;
    }
}

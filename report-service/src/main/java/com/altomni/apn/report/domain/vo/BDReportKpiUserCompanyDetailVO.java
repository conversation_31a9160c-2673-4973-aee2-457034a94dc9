package com.altomni.apn.report.domain.vo;

import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;


public class BDReportKpiUserCompanyDetailVO implements Serializable {

    private static final long serialVersionUID = 4442477104358727842L;

    private Long id;

    private String companyName;

    private String active;

    private String salesLeadName;

    private String bdOwnerName;

    private String amName;

    private String coAmName;

    private List<UserCountryVO> coAmList;

    private String createdDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getActive() {
        return active;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public String getSalesLeadName() {
        return salesLeadName;
    }

    public void setSalesLeadName(String salesLeadName) {
        this.salesLeadName = salesLeadName;
    }

    public String getBdOwnerName() {
        return bdOwnerName;
    }

    public void setBdOwnerName(String bdOwnerName) {
        this.bdOwnerName = bdOwnerName;
    }

    public String getAmName() {
        return amName;
    }

    public void setAmName(String amName) {
        this.amName = amName;
    }

    public String getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
    }

    public String getCoAmName() {
        return coAmName;
    }

    public void setCoAmName(String coAmName) {
        this.coAmName = coAmName;
    }

    public List<UserCountryVO> getCoAmList() {
        return coAmList;
    }

    public void setCoAmList(List<UserCountryVO> coAmList) {
        this.coAmList = coAmList;
    }
}

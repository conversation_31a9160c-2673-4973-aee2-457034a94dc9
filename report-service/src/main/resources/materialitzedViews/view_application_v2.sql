-- 流程相关的事实表和维度表关联视图，给流程相关的物化视图提供数据

create view view_application_v2 as
(
SELECT `ods_apn`.`dim`.`tenant_id`,
       `ods_apn`.`dim`.`company_id`,
       `ods_apn`.`dim`.`job_id`,
       `ods_apn`.`dim`.`job_pteam_id`,
       `ods_apn`.`dim`.`talent_recruitment_process_id`,
       `ods_apn`.`dim`.`talent_id`,
       `ods_apn`.`dim`.`ai_score`,
       `ods_apn`.`dim`.`recommend_feedback_id`,
       `ods_apn`.`pt`.`id`                                                AS `team_id`,
       `ods_apn`.`pt`.`name`                                              AS `team_name`,
       `ods_apn`.`fact`.`user_id`,
       concat(`ods_apn`.`u`.`first_name`, ' ', `ods_apn`.`u`.`last_name`) AS `user_name`,
       `ods_apn`.`u`.`activated`                                          AS `user_activated`,
       `ods_apn`.`fact`.`user_role`,
       `ods_apn`.`fact`.`node_id`,
       `ods_apn`.`fact`.`node_type`,
       `ods_apn`.`fact`.`node_status`,
       `ods_apn`.`fact`.`progress`,
       `ods_apn`.`fact`.`final_round`,
       `ods_apn`.`fact`.`add_date`,
       `ods_apn`.`fact`.`event_date`
FROM `ods_apn`.`view_application_fact` AS `fact`
         INNER JOIN `ods_apn`.`mv_application_dimension` AS `dim` ON `ods_apn`.`fact`.`talent_recruitment_process_id` =
                                                                     `ods_apn`.`dim`.`talent_recruitment_process_id`
         INNER JOIN `ods_apn`.`user` AS `u` ON `ods_apn`.`fact`.`user_id` = `ods_apn`.`u`.`id`
         INNER JOIN `ods_apn`.`permission_user_team` AS `put`
                    ON (`ods_apn`.`u`.`id` = `ods_apn`.`put`.`user_id`) AND (`ods_apn`.`put`.`is_primary` = 1)
         INNER JOIN `ods_apn`.`permission_team` AS `pt` ON `ods_apn`.`put`.`team_id` = `ods_apn`.`pt`.`id`);


package com.altomni.apn.common.domain.talent;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.dict.TalentIndustryRelation;
import com.altomni.apn.common.domain.dict.TalentJobFunctionRelation;
import com.altomni.apn.common.domain.dict.TalentLanguageRelation;
import com.altomni.apn.common.domain.dict.TalentWorkAuthorizationRelation;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.InfoUtils;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.*;

/**
 * A Talent.
 */
@ApiModel(description = "Talent describes the person potentially seek jobs. " +
        "Talent profiles belong to tenant. Each tenant has its own version of talents. " +
        "Multiple tenants will have different talents, even they may be the same actual person. " +
        "Talent comes from referred by sourcer or crawled from job sites, e.g. LinkedIn " +
        "Talent may or may not have a job associated when created / updated " +
        "When has job associated, talent is considered talentType. " +
        "When creating talent, all Talent* associated entities are allowed and will be inserted. After that, need to use " +
        "the related entity to create/update them.")
@Entity
@Data
@Table(name = "talent")
@AllArgsConstructor
@NoArgsConstructor
@JsonIdentityInfo(
        generator = ObjectIdGenerators.PropertyGenerator.class,
        property = "id")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentV3 extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("tenantId", "fullName"));

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The tenant id talent belongs to. This is saved from the user's tenant when creating talent.")
    @Column(name = "tenant_id", nullable = false, updatable = false)
    private Long tenantId;

    @ApiModelProperty(value = "first name. required in US.", required = true)
    @Column(name = "first_name")
    private String firstName;

    @ApiModelProperty(value = "last name. required in CN.", required = true)
    @Column(name = "last_name")
    private String lastName;

    @ApiModelProperty(value = "full name. Either firstName & lastName (in US) or full name (in CN) are required. Either way, " +
            "the full name can not be null.")
    @Column(name = "full_name", nullable = false)
    private String fullName;

    @ApiModelProperty(value = "url link to photo")
    @Column(name = "photo_url")
    private String photoUrl;

    @ApiModelProperty(value = "birthday. Format: yyyy-MM-dd")
    @Column(name = "birthday")
    private String birthday;

    @ApiModelProperty(value = "motivation enum id")
    @Column(name = "motivation_id")
    private Integer motivationId;

    @ApiModelProperty(value = "whether talent is active. Inactive talent means the profile is deleted, and should not show " +
            "up in any search. Default is true.")
    @Column(name = "active")
    private Boolean active = true;


    @ApiModelProperty(value = "one or more job functions")
    @OneToMany(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JoinColumn(name = "talent_id")
    private Set<TalentJobFunctionRelation> jobFunctions;


    @ApiModelProperty(value = "one or more languages")
    @OneToMany(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JoinColumn(name = "talent_id")
    private Set<TalentLanguageRelation> languages;


    @ApiModelProperty(value = "one or more industries")
    @OneToMany(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JoinColumn(name = "talent_id")
    private Set<TalentIndustryRelation> industries;

    @ApiModelProperty(value = "one workAuthorization")
    @OneToMany(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JoinColumn(name = "talent_id")
    private Set<TalentWorkAuthorizationRelation> workAuthorization;


    @OneToOne(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "additional_info_id")
    private TalentAdditionalInfo talentAdditionalInfo;

    @ApiModelProperty(value = "last edited time of talent, talentSkill, userFavoriteJob and companyContact")
    @Column(name = "last_edited_time")
    private Instant lastEditedTime;

    @ApiModelProperty(value = "last successful sync to ES")
    @Column(name = "last_sync_time")
    private Instant lastSyncTime;

    @ApiModelProperty(value = "weather sync to es has been paused")
    @Column(name = "sync_paused")
    private Boolean syncPaused = Boolean.FALSE;

    @ApiModelProperty(value = "if this talent is owned by the tenant")
    @Column(name = "owned_by_tenants")
    private Long ownedByTenants;

    @Column(name = "is_need_sync_hr")
    private Boolean isNeedSyncHr;

    public static TalentV3 fromTalentDTO(TalentDTOV3 dto) {
        TalentV3 talent = Convert.convert(TalentV3.class, dto);
        talent.setAdditionalInfoId(dto.getAdditionalInfoId());
        String extendedInfo = dto.getExtendedInfo() != null ? dto.getExtendedInfo() : generateExtendedInfo(dto);
        talent.setTalentExtendedInfo(extendedInfo);
        talent.setTalentLocalExtendedInfo(generateLocalExtendedInfo(dto));

        return talent;
    }

    private static String generateLocalExtendedInfo(TalentDTOV3 dto) {
        JSONObject extendedInfo = new JSONObject();
        if (dto.getSourceOwnershipPeriod() != null) {
            extendedInfo.put("sourceOwnershipPeriod", dto.getSourceOwnershipPeriod());
        }
        return JSONUtil.toJsonStr(extendedInfo);
    }

    public static String generateExtendedInfo(TalentDTOV3 dto) {
        TalentDTOV3 nonRelationData = TalentDTOV3.getNonRelationData(dto);
        return transferExperiences(JSONObject.toJSONString(nonRelationData, (PropertyFilter) (o, s, o1) -> ObjectUtil.isNotEmpty(o1)));
    }

    private static String transferExperiences(String jsonStr) {
        if (JSONUtil.isJson(jsonStr)) {
            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(jsonStr);
            if (jsonObject.containsKey("experiences")) {
                JSONArray experiences = jsonObject.getJSONArray("experiences");
                for (int i = 0; i < experiences.size(); i++) {
                    cn.hutool.json.JSONObject experience = experiences.getJSONObject(i);
                    if (experience.containsKey("activeCompanyId")) {
                        experience.put("activeCompanyId", experience.getStr("activeCompanyId"));
                    }
                    if (experience.containsKey("companyId")) {
                        experience.put("companyId", experience.getStr("companyId"));
                    }
                }
            }
            jsonStr = jsonObject.toString();
        }
        return jsonStr;
    }

    public static String mergeExtendedInfo(String source, String target){
        JSONObject source1 = JSONObject.parseObject(source);
        InfoUtils.replenishNullField(source1, TalentDTOV3.class);
        Object merge = InfoUtils.merge(source1, JSONObject.parseObject(target));
        return JSON.toJSONString(merge, (PropertyFilter) (o, s, o1) -> ObjectUtil.isNotEmpty(o1));
    }

    //创建，更新候选人使用的合并方法
    //前端传输的schema必须完整，string为空传空string，某对象没填要传空object {}，Array没填要传空Array，数字没填要传null
    public static String mergeExtendedInfoV3(String source, String toBeMerged){
        JSONObject source1 = JSONObject.parseObject(source);
        Object merge = merge(source1, JSONObject.parseObject(toBeMerged));
        return JSON.toJSONString(merge, (PropertyFilter) (o, s, o1) -> ObjectUtil.isNotEmpty(o1));
    }

    public static Object merge(Object source, Object toBeMerged) {
        try {
            if (source == null) {
                return null;
            }
            if (toBeMerged == null) {
                return source;
            }

            if (source instanceof JSONObject) {
                JSONObject sourceJSONObject = (JSONObject) source;
                JSONObject targetJSONObject = (JSONObject) toBeMerged;
                if(sourceJSONObject.isEmpty()) {
                    return sourceJSONObject;
                }

                if (sourceJSONObject.containsKey("gt") || sourceJSONObject.containsKey("lt") ||
                        sourceJSONObject.containsKey("gte") || sourceJSONObject.containsKey("lte")) {
                    return source;
                }
                JSONObject newJSONObject = new JSONObject();

                for (String key : targetJSONObject.keySet()) {
                    if (!sourceJSONObject.containsKey(key)) {
                        newJSONObject.put(key, targetJSONObject.get(key));
                    }
                }

                for (String key : sourceJSONObject.keySet()) {
                    Object value = sourceJSONObject.get(key);
                    Object result = merge(value, targetJSONObject.get(key));
                    if (ObjectUtil.isNotEmpty(result)) {
                        newJSONObject.put(key, result);
                    } else {
                        newJSONObject.remove(key);
                    }
                }
                return newJSONObject;

            } else if (source instanceof com.alibaba.fastjson.JSONArray) {
                Map map = new HashMap<>();
                com.alibaba.fastjson.JSONArray sourceJSONArray = (com.alibaba.fastjson.JSONArray) source;
                com.alibaba.fastjson.JSONArray targetJSONArray = (com.alibaba.fastjson.JSONArray) toBeMerged;
                if(sourceJSONArray.isEmpty()) {
                    return sourceJSONArray;
                }
                long maxId = 0;

                for (Object obj : targetJSONArray) {
                    if (obj instanceof JSONObject) {
                        JSONObject objJSONObject = (JSONObject) obj;
                        Long id = objJSONObject.getLong("id");
                        if (id != null) {
                            if(id > maxId) {
                                maxId = id;
                            }
                            map.put(id, objJSONObject);
                        }
                    }
                }

                com.alibaba.fastjson.JSONArray newJSONArray = new com.alibaba.fastjson.JSONArray();
                for (Object obj : sourceJSONArray) {
                    if (obj instanceof JSONObject) {
                        JSONObject objJSONObject = (JSONObject) obj;
                        Long id = objJSONObject.getLong("id");
                        Object result;
                        if (id != null) {
                            result = merge(objJSONObject, map.get(id));
                        } else {
                            maxId++;
                            id = maxId;
                            result = obj;
                        }
                        if (ObjectUtil.isNotEmpty(result)) {
                            ((JSONObject) result).put("id", id);
                            newJSONArray.add(result);
                        }
                    } else {
                        Object result = obj;
                        //和parser解析结果时，"yearRanges":[null,null,null],"proficiencies":[null,"EXPERT","EXPERT"]， 这种数组中的null不能过滤。所以下面不做非空判断
//                        if (ObjectUtil.isNotEmpty(result)) {
                        newJSONArray.add(result);
//                        }
                    }
                }
                return newJSONArray;

            } else {
                return source;
            }
        } catch (Exception e) {
            return null;
        }
    }

//    public static TalentV3 fromTalentPublicDTO(TalentPublicDTO dto) {
//        return fromTalentDTO(dto);
//    }

    /**
     * This should only happen for English
     * Chinese should just set full name, ignore the first & last name
     */
    public void setFullName() {
        if (this.firstName == null && this.lastName == null) {
            throw new CustomParameterizedException("Either full name or first name and last name are required");
        }
        if(this.firstName == null) {
            this.fullName = this.lastName;
        } else if(this.lastName == null) {
            this.fullName = this.firstName;
        } else {
            this.fullName = CommonUtils.formatFullName(firstName, lastName);
        }
    }

    public String getTalentExtendedInfo() {
        String extendedInfo = null;
        if (talentAdditionalInfo != null) {
            extendedInfo = talentAdditionalInfo.getExtendedInfo();
        }
        return extendedInfo == null ? "" : extendedInfo;
    }


    public String getTalentLocalExtendedInfo() {
        String extendedInfo = null;
        if (talentAdditionalInfo != null) {
            extendedInfo = talentAdditionalInfo.getLocalExtendedInfo();
        }
        return extendedInfo == null ? "" : extendedInfo;
    }

    public void setTalentExtendedInfo(String extendedInfo) {
        if (StrUtil.isNotEmpty(extendedInfo)) {
            if (talentAdditionalInfo == null) {
                talentAdditionalInfo = new TalentAdditionalInfo();
            }
            talentAdditionalInfo.setExtendedInfo(extendedInfo);
        }
    }

    public void setTalentLocalExtendedInfo(String extendedInfo) {
        if (StrUtil.isNotEmpty(extendedInfo)) {
            if (talentAdditionalInfo == null) {
                talentAdditionalInfo = new TalentAdditionalInfo();
            }
            talentAdditionalInfo.setLocalExtendedInfo(extendedInfo);
        }
    }

    public void setAdditionalInfoId(Long id) {
        if (id != null) {
            if (talentAdditionalInfo == null) {
                talentAdditionalInfo = new TalentAdditionalInfo();
            }
            talentAdditionalInfo.setId(id);
        }
    }

    public Long getAdditionalInfoId() {
        if (talentAdditionalInfo == null) {
            return null;
        }
        return talentAdditionalInfo.getId();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TalentV3 talentV3 = (TalentV3) o;
        if (talentV3.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), talentV3.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}

package com.altomni.apn.common.domain.enumeration.search;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ModuleType enumeration.
 */
public enum ModuleType implements ConvertedEnum<Integer> {
    JOB(0, "JOB"),
    CA<PERSON><PERSON>AT<PERSON>(1, "TALENT_POOL"),
    MY_PIPELINE(2, "TALENT_POOL"),
    CANDIDATE_ADD_TO_JOB(3, "ALL_JOB"),
    JOB_PICK_CANDIDATE(4, "TALENT_POOL"),
    COMPANY(5, "ALL_JOB"),
    REPOR<PERSON>(6, "REPORTS"),
    COMMON_POOL(7, "COMMON_POOL"),
    DASHBOARD_DOCUMENT(8,"DASHBOARD_DOCUMENT"),
    DASHBOARD_PACKAGE(9,"DASH<PERSON>ARD_PACKAGE"),

    CONTRACTOR_HIRE(10, "CONTRACTOR_HIRE"),
    CONTRACTOR_TERMINATION(11, "CONTRACTOR_TERMINATION"),
    TIMESHEET_BY_STATUS_WITHIN_A_PERIOD(12, "TIMESHEET_BY_STATUS_WITHIN_A_PERIOD"),

    INTERNAL_INVOICE(13, "INTERNAL_INVOICE"),
    GROUP_INVOICE(14, "GROUP_INVOICE"),

    BIO_COMMERCIAL_TALENT_POOL(15, "BIO_COMMERCIAL_TALENT_POOL"),

    COMPANY_POOL(16,"COMPANY"),

    HOT_LIST_TALENT(17, "HOT_LIST_TALENT"),

    AGENCY_JOB(18, "AGENCY_JOB"),

    MY_JOB(19, "JOB"),

    //候选人职位文件夹中的候选人用
    RELATE_JOB_FOLDER(100, "JOB_FOLDER_TALENT"),

    //候选人职位文件夹中用
    JOB_FOLDER(101, "JOB_FOLDER"),

    //搜索文件夹中用
    SEARCH_FOLDER(102, "TALENT_POOL"),

    PRIVATE_JOB(200, "PRIVATE_JOB"),
    ALL_JOB(201, "ALL_JOB"), // regular job + private job

    //report 用
    //kpi report by user
    KPI_REPORT_BY_USER( 250, "KPI_REPORT_BY_USER"),
    KPI_REPORT_BY_USER_JOB_DETAIL( 251, "KPI_REPORT_BY_USER_JOB_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_DETAIL( 252, "KPI_REPORT_BY_USER_TALENT_DETAIL"),
    KPI_REPORT_BY_USER_SUBMIT_TO_JOB_DETAIL( 253, "KPI_REPORT_BY_USER_SUBMIT_TO_JOB_DETAIL"),
    KPI_REPORT_BY_USER_SUBMIT_TO_CLIENT_DETAIL( 254, "KPI_REPORT_BY_USER_SUBMIT_TO_CLIENT_DETAIL"),
    KPI_REPORT_BY_USER_INTERVIEW_1_DETAIL( 255, "KPI_REPORT_BY_USER_INTERVIEW_1_DETAIL"),
    KPI_REPORT_BY_USER_INTERVIEW_2_DETAIL( 256, "KPI_REPORT_BY_USER_INTERVIEW_2_DETAIL"),
    KPI_REPORT_BY_USER_INTERVIEW_FINAL_DETAIL( 257, "KPI_REPORT_BY_USER_INTERVIEW_FINAL_DETAIL"),
    KPI_REPORT_BY_USER_INTERVIEW_DETAIL( 258, "KPI_REPORT_BY_USER_INTERVIEW_DETAIL"),
    KPI_REPORT_BY_USER_OFFER_DETAIL( 259, "KPI_REPORT_BY_USER_OFFER_DETAIL"),
    KPI_REPORT_BY_USER_OFFER_ACCEPT_DETAIL( 260, "KPI_REPORT_BY_USER_OFFER_ACCEPT_DETAIL"),
    KPI_REPORT_BY_USER_ONBOARD_DETAIL( 261, "KPI_REPORT_BY_USER_ONBOARD_DETAIL"),
    KPI_REPORT_BY_USER_ELIMINATE_DETAIL( 262, "KPI_REPORT_BY_USER_ELIMINATE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_CALL_NOTE_DETAIL( 263, "KPI_REPORT_BY_USER_TALENT_CALL_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_EMAIL_NOTE_DETAIL( 264, "KPI_REPORT_BY_USER_TALENT_EMAIL_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_PERSON_NOTE_DETAIL( 265, "KPI_REPORT_BY_USER_TALENT_PERSON_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_VIDEO_NOTE_DETAIL( 266, "KPI_REPORT_BY_USER_TALENT_VIDEO_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_OTHER_NOTE_DETAIL( 267, "KPI_REPORT_BY_USER_TALENT_OTHER_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_PIPELINE_NOTE_DETAIL( 268, "KPI_REPORT_BY_USER_TALENT_PIPELINE_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_APN_PRO_NOTE_DETAIL( 269, "KPI_REPORT_BY_USER_TALENT_APN_PRO_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_CREATE_COMPANY_DETAIL(270,"KPI_REPORT_BY_USER_CREATE_COMPANY_DETAIL"),
    KPI_REPORT_BY_USER_UPGRADE_TO_CLIENT_DETAIL(271,"KPI_REPORT_BY_USER_UPGRADE_TO_CLIENT_DETAIL"),
    KPI_REPORT_BY_USER_INTERVIEW_APPOINTMENTS_DETAIL(272, "KPI_REPORT_BY_USER_INTERVIEW_APPOINTMENTS_DETAIL"),
    KPI_REPORT_BY_USER_INTERVIEW_TWO_MORE_DETAIL(273, "KPI_REPORT_BY_USER_INTERVIEW_TWO_MORE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_ICI_NOTE_DETAIL(274, "KPI_REPORT_BY_USER_TALENT_ICI_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_VOIP(400, "KPI_REPORT_BY_USER_VOIP"),
    KPI_REPORT_BY_USER_VOIP_DETAIL(401, "KPI_REPORT_BY_USER_VOIP_DETAIL"),

    //h1 h2 detail
    H1_REPORT_DETAIL(410, "H1_REPORT_DETAIL"),
    H2_REPORT_DETAIL(411, "H2_REPORT_DETAIL"),



    //kpi report by company
    KPI_REPORT_BY_COMPANY( 300, "KPI_REPORT_BY_COMPANY"),
    KPI_REPORT_BY_COMPANY_JOB_DETAIL( 301, "KPI_REPORT_BY_COMPANY_JOB_DETAIL"),
    KPI_REPORT_BY_COMPANY_TALENT_DETAIL( 302, "KPI_REPORT_BY_COMPANY_TALENT_DETAIL"),
    KPI_REPORT_BY_COMPANY_SUBMIT_TO_JOB_DETAIL( 303, "KPI_REPORT_BY_COMPANY_SUBMIT_TO_JOB_DETAIL"),
    KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL( 304, "KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL"),
    KPI_REPORT_BY_COMPANY_INTERVIEW_1_DETAIL( 305, "KPI_REPORT_BY_COMPANY_INTERVIEW_1_DETAIL"),
    KPI_REPORT_BY_COMPANY_INTERVIEW_2_DETAIL( 306, "KPI_REPORT_BY_COMPANY_INTERVIEW_2_DETAIL"),
    KPI_REPORT_BY_COMPANY_INTERVIEW_FINAL_DETAIL( 307, "KPI_REPORT_BY_COMPANY_INTERVIEW_FINAL_DETAIL"),
    KPI_REPORT_BY_COMPANY_INTERVIEW_DETAIL( 308, "KPI_REPORT_BY_COMPANY_INTERVIEW_DETAIL"),
    KPI_REPORT_BY_COMPANY_OFFER_DETAIL( 309, "KPI_REPORT_BY_COMPANY_OFFER_DETAIL"),
    KPI_REPORT_BY_COMPANY_OFFER_ACCEPT_DETAIL( 310, "KPI_REPORT_BY_COMPANY_OFFER_ACCEPT_DETAIL"),
    KPI_REPORT_BY_COMPANY_ONBOARD_DETAIL( 311, "KPI_REPORT_BY_COMPANY_ONBOARD_DETAIL"),
    KPI_REPORT_BY_COMPANY_ELIMINATE_DETAIL( 312, "KPI_REPORT_BY_COMPANY_ELIMINATE_DETAIL"),
    KPI_REPORT_BY_COMPANY_JOB_NOTE_DETAIL( 313, "KPI_REPORT_BY_COMPANY_JOB_NOTE_DETAIL"),
    KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL_THIS_WEEK(314, "KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL_THIS_WEEK"),
    KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL_LAST_WEEK(315, "KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL_LAST_WEEK"),
    KPI_REPORT_BY_COMPANY_BD_PROGRESS_NOTES(316, "KPI_REPORT_BY_COMPANY_BD_PROGRESS_NOTES"),
    KPI_REPORT_BY_COMPANY_NOTES(317, "KPI_REPORT_BY_COMPANY_NOTES"),

    USER_ADOPTION_REPORT(318, "USER_ADOPTION_REPORT"),
    USER_ADOPTION_REPORT_USER_ACTIVE_DURATION(319, "USER_ADOPTION_REPORT_USER_ACTIVE_DURATION"),
    USER_ADOPTION_REPORT_CALLS_CANDIDATE(320, "USER_ADOPTION_REPORT_CALLS_CANDIDATE"),
    USER_ADOPTION_REPORT_CALLS_CONTACT(321, "USER_ADOPTION_REPORT_CALLS_CONTACT"),
    USER_ADOPTION_REPORT_NOTES_CANDIDATE(322, "USER_ADOPTION_REPORT_NOTES_CANDIDATE"),
    USER_ADOPTION_REPORT_NOTES_CONTACT(323, "USER_ADOPTION_REPORT_NOTES_CONTACT"),
    USER_ADOPTION_REPORT_EMAILS_CANDIDATE(324, "USER_ADOPTION_REPORT_EMAILS_CANDIDATE"),
    USER_ADOPTION_REPORT_EMAILS_CONTACT(325, "USER_ADOPTION_REPORT_EMAILS_CONTACT"),
    USER_ADOPTION_REPORT_SUBMIT_TO_JOB(326, "USER_ADOPTION_REPORT_SUBMIT_TO_JOB"),
    USER_ADOPTION_REPORT_INTERVIEW(327, "USER_ADOPTION_REPORT_INTERVIEW"),
    USER_ADOPTION_REPORT_ONBOARD(328, "USER_ADOPTION_REPORT_ONBOARD"),


    ;

    // static resolving:
    public static final ReverseEnumResolver<ModuleType, Integer> resolver =
            new ReverseEnumResolver<>(ModuleType.class, ModuleType::toDbValue);
    private final int dbValue;
    private final String name;

    ModuleType(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    public static ModuleType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }
}

package com.altomni.apn.company.service.business.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.common.dto.company.AccountBusinessDTO;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.enums.EnumCompanyServiceTypeService;
import com.altomni.apn.common.service.enums.EnumSalesLeadSourceService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.business.*;
import com.altomni.apn.company.domain.contract.ContractBusinessRelation;
import com.altomni.apn.company.domain.enumeration.business.BusinessProgress;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.altomni.apn.company.domain.vm.UserBasicVM;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.repository.business.*;
import com.altomni.apn.company.repository.contract.ContractBusinessRelationRepository;
import com.altomni.apn.company.repository.user.UserServiceRepository;
import com.altomni.apn.company.service.business.AccountBusinessService;
import com.altomni.apn.company.service.user.UserService;
import com.altomni.apn.company.vo.business.OwnerVO;
import com.altomni.apn.company.vo.contact.ContactVO;
import com.altomni.apn.company.vo.business.AccountBusinessVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AccountBusinessServiceImpl implements AccountBusinessService {

    private final Double CLIENT_ACCOUNT_PROGRESS = 1.20;

    @Resource
    private AccountBusinessRepository accountBusinessRepository;

    @Resource
    private AccountBusinessMigrateRepository accountBusinessMigrateRepository;

    @Resource
    private SalesLeadClientContactRepository salesLeadClientContactRepository;

    @Resource
    private AccountBusinessContactRelationRepository accountBusinessContactRelationRepository;

    @Resource
    private AccountBusinessAdministratorRepository accountBusinessAdministratorRepository;

    @Resource
    private CompanyRepository companyRepository;

    @Resource
    private UserService userService;

    @Resource
    private EnumCommonService enumCommonService;

    @Resource
    private EnumCompanyServiceTypeService enumCompanyServiceTypeService;

    @Resource
    private EnumSalesLeadSourceService enumSalesLeadSourceService;

    @Resource
    private UserServiceRepository userServiceRepository;

    @Resource
    private ContractBusinessRelationRepository contractBusinessRelationRepository;

    @Resource
    private AccountBusinessServiceTypeRelationRepository accountBusinessServiceTypeRelationRepository;

    private final String BUSINESS = "business";

    private final String SERVICE_TYPE_RELATION = "serviceTypeRelations";

    private final String ADMINISTRATOR = "administrators";

    private final String BUSINESS_CONTACT_RELATION = "businessContactRelations";

    private final String BUSINESS_CONTRACT_RELATION = "businessContractRelations";

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public SalesLeadVO createSalesLead(CompanySalesLeadProspectDTO salesLeadDTO) {
//        Company company = checkCompanyPermission(salesLeadDTO.getCompanyId());
//        checkCompanyServiceType(salesLeadDTO.getCompanyServiceTypes());
//        checkCompanyContact(salesLeadDTO.getSalesLeadClientContacts(), company.getId());
//        checkSalesLeadUser(salesLeadDTO.getSalesLeadsOwners().stream().map(SalesLeadAdministratorDTO::getId).collect(Collectors.toList()));
//        checkSalesLeadUser(salesLeadDTO.getSalesLeadsBdOwners().stream().map(SalesLeadAdministratorDTO::getId).collect(Collectors.toList()));
//        checkSalesLeadSource(Arrays.asList(salesLeadDTO.getLeadSource()));
//
//        AccountBusiness accountBusiness = AccountBusiness.fromSalesLeadDetailDTO(salesLeadDTO);
//        accountBusiness.setEnumCompanyServiceTypes(new HashSet<>(enumCompanyServiceTypeService.findAllByIds(salesLeadDTO.getCompanyServiceTypes())));
//        AccountBusiness createdAccountBusiness = accountBusinessRepository.save(accountBusiness);
//
//        SalesLeadVO salesLeadVO = SalesLeadVO.fromSalesLead(createdAccountBusiness);
//
//        accountBusinessContactRelationRepository.saveAll(salesLeadDTO.getSalesLeadClientContacts().stream().map(o -> new AccountBusinessContactRelation(o, createdAccountBusiness.getId())).collect(Collectors.toList()));
//
//        List<BusinessFlowAdministrator> businessFlowAdministrators = salesLeadDTO.getSalesLeadsOwners().stream().map(o -> new BusinessFlowAdministrator(createdAccountBusiness.getId(), o.getId(), SalesLeadRoleType.SALES_LEAD_OWNER, salesLeadDTO.getCompanyId())).collect(Collectors.toList());
//        businessFlowAdministrators.addAll(salesLeadDTO.getSalesLeadsBdOwners().stream().map(o -> new BusinessFlowAdministrator(createdAccountBusiness.getId(), o.getId(), SalesLeadRoleType.BUSINESS_DEVELOPMENT, salesLeadDTO.getCompanyId())).collect(Collectors.toList()));
//        accountBusinessAdministratorRepository.saveAll(businessFlowAdministrators);
//
//        return salesLeadVO;
//    }

//    @Override
//    public List<SalesLeadVO> getAllProspectSalesLeadsByCompany(Long companyId, Boolean upgraded) {
//        Optional<Company> companyOptional = companyRepository.findById(companyId);
//        if(!companyOptional.isPresent()) {
//            throw new CustomParameterizedException("The salesLead's companyId: " +  companyId + " cannot find correspond company!");
//        }
//
//        List<AccountBusiness> accountBusinessList = accountBusinessRepository.findAllByCompanyId(companyId);
//        List<SalesLeadVO> salesLeadDetails = new ArrayList<>();
//        if (upgraded == null) {
//            salesLeadDetails = accountBusinessList.stream().map(SalesLeadVO::fromSalesLead).collect(Collectors.toList());
//        } else {
//            salesLeadDetails = upgraded ? accountBusinessList.stream().filter(o -> o.getAccountProgress().compareTo(BigDecimal.valueOf(CLIENT_ACCOUNT_PROGRESS)) == 0).map(SalesLeadVO::fromSalesLead).collect(Collectors.toList())
//                    : accountBusinessList.stream().filter(o -> o.getAccountProgress().compareTo(BigDecimal.valueOf(CLIENT_ACCOUNT_PROGRESS)) < 0).map(SalesLeadVO::fromSalesLead).collect(Collectors.toList());
//        }
//        if (CollUtil.isEmpty(salesLeadDetails)) {
//            return new ArrayList<>();
//        }
//        List<BusinessFlowAdministrator> businessFlowAdministratorList = accountBusinessAdministratorRepository.findAllBySalesLeadIdInAndSalesLeadRoleTypeIn(salesLeadDetails.stream().map(SalesLeadVO::getId).collect(Collectors.toList()), Arrays.asList(SalesLeadRoleType.SALES_LEAD_OWNER, SalesLeadRoleType.BUSINESS_DEVELOPMENT));
//        Map<Long, List<BusinessFlowAdministrator>> salesLeadAdministratorMap = businessFlowAdministratorList.stream().collect(Collectors.groupingBy(BusinessFlowAdministrator::getSalesLeadId));
//        List<EntityNameVM> entityNameList = userServiceRepository.findUserNameByIds(businessFlowAdministratorList.stream().map(BusinessFlowAdministrator::getUserId).distinct().collect(Collectors.toList()));
//        Map<Long, String> userNameMap = entityNameList.stream().collect(Collectors.toMap(EntityNameVM::getId, o -> CommonUtils.formatFullName(o.getFirstName(), o.getLastName())));
//        salesLeadDetails.forEach(item -> {
//            if (salesLeadAdministratorMap.containsKey(item.getId())) {
//                List<BusinessFlowAdministrator> itemBusinessFlowAdministratorList = salesLeadAdministratorMap.get(item.getId());
//                item.setSalesLeadsOwners(itemBusinessFlowAdministratorList.stream().filter(admin -> admin.getSalesLeadRoleType().equals(SalesLeadRoleType.SALES_LEAD_OWNER)).map(o -> new SalesLeadAdministratorVO(o.getUserId(), userNameMap.get(o.getUserId()))).collect(Collectors.toList()));
//                item.setSalesLeadsBdOwners(itemBusinessFlowAdministratorList.stream().filter(admin -> admin.getSalesLeadRoleType().equals(SalesLeadRoleType.BUSINESS_DEVELOPMENT)).map(o -> new SalesLeadAdministratorVO(o.getUserId(), userNameMap.get(o.getUserId()))).collect(Collectors.toList()));
//            }
//        });
//        return salesLeadDetails;
//    }

//    /*TODO: add update salesLead individually, not through updating company */
//    @Override
//    public SalesLeadDTO updateSalesLead(Set<SalesLeadDetailVM> salesLeadDetailVMs) {
//        SalesLeadDTO salesLeadDTO = new SalesLeadDTO();
//        return salesLeadDTO;
//    }

    @Override
    public List<AccountBusinessMigrate> saveAccountBusiness(JSONObject jsonObject) {
        if (!jsonObject.containsKey(BUSINESS)) {
            return null;
        }
        log.info("[addCompanyCalendar] business:{}",JSONUtil.toJsonStr(jsonObject.getJSONObject(BUSINESS)));
        List<AccountBusinessMigrate> businessList = jsonObject.containsKey(BUSINESS) ? jsonObject.getJSONArray(BUSINESS).toList(AccountBusinessMigrate.class) : new ArrayList<>();
        log.info("[addCompanyCalendar] business to bean:{}",JSONUtil.toJsonStr(businessList));
        List<AccountBusinessServiceTypeRelation> serviceTypeRelationList = jsonObject.containsKey(SERVICE_TYPE_RELATION) ? jsonObject.getJSONArray(SERVICE_TYPE_RELATION).toList(AccountBusinessServiceTypeRelation.class) : new ArrayList<>();
        List<BusinessFlowAdministrator> businessFlowAdministratorList = jsonObject.containsKey(ADMINISTRATOR) ? jsonObject.getJSONArray(ADMINISTRATOR).stream().map(o -> {
            JSONObject item = JSONUtil.parseObj(o);
            BusinessFlowAdministrator businessFlowAdministrator = JSONUtil.toBean(item, BusinessFlowAdministrator.class);
            businessFlowAdministrator.setCompanyId(item.getLong("accountCompanyId"));
            return businessFlowAdministrator;
        }).toList() : new ArrayList<>();
        List<AccountBusinessContactRelation> contactRelationList = jsonObject.containsKey(BUSINESS_CONTACT_RELATION) ? jsonObject.getJSONArray(BUSINESS_CONTACT_RELATION).toList(AccountBusinessContactRelation.class) : new ArrayList<>();
        List<ContractBusinessRelation> contractRelationList = jsonObject.containsKey(BUSINESS_CONTRACT_RELATION) ? jsonObject.getJSONArray(BUSINESS_CONTRACT_RELATION).toList(ContractBusinessRelation.class) : new ArrayList<>();

        List<Long> businessIds = businessList.stream().map(AccountBusinessMigrate::getId).toList();
        Map<Long, AccountBusinessServiceTypeRelation> serviceTypeRelationMap = serviceTypeRelationList.stream().collect(Collectors.toMap(AccountBusinessServiceTypeRelation::getId, o -> o));
        Map<Long, BusinessFlowAdministrator> administratorMap = businessFlowAdministratorList.stream().collect(Collectors.toMap(BusinessFlowAdministrator::getId, o -> o));
        Map<Long, AccountBusinessContactRelation> contactRelationMap = contactRelationList.stream().collect(Collectors.toMap(AccountBusinessContactRelation::getId, o -> o));
        Map<Long, ContractBusinessRelation> contractRelationMap = contractRelationList.stream().collect(Collectors.toMap(ContractBusinessRelation::getId, o -> o));

        List<AccountBusinessServiceTypeRelation> existedServiceTypeRelationList = accountBusinessServiceTypeRelationRepository.findAllByAccountBusinessIdIn(businessIds);
        List<BusinessFlowAdministrator> existedBusinessFlowAdministratorList = accountBusinessAdministratorRepository.findAllByAccountBusinessIdInForCrm(businessIds);
        List<AccountBusinessContactRelation> existedBusinessContactRelationList = accountBusinessContactRelationRepository.findAllByAccountBusinessIdIn(businessIds);
        List<ContractBusinessRelation> existedContractBusinessRelationList = contractBusinessRelationRepository.findAllByAccountBusinessIdIn(businessIds);

        List<Long> deleteServiceTypeRelationIds = existedServiceTypeRelationList.stream().map(AccountBusinessServiceTypeRelation::getId).filter(o -> !serviceTypeRelationMap.containsKey(o)).toList();
        List<Long> deleteAdministratorIds = existedBusinessFlowAdministratorList.stream().map(BusinessFlowAdministrator::getId).filter(o -> !administratorMap.containsKey(o)).toList();
        List<Long> deleteContactRelationIds = existedBusinessContactRelationList.stream().map(AccountBusinessContactRelation::getId).filter(o -> !contactRelationMap.containsKey(o)).toList();
        List<Long> deleteContractRelationIds = existedContractBusinessRelationList.stream().map(ContractBusinessRelation::getId).filter(o -> !contractRelationMap.containsKey(o)).toList();
        log.info("save crm business: {}, contactRelationList : {}", businessIds, contactRelationList);
        existedServiceTypeRelationList.forEach(item -> {
            if (serviceTypeRelationMap.containsKey(item.getId())) {
                ServiceUtils.myCopyProperties(serviceTypeRelationMap.get(item.getId()), item);
            }
        });
        existedBusinessFlowAdministratorList.forEach(item -> {
            if (administratorMap.containsKey(item.getId())) {
                ServiceUtils.myCopyProperties(administratorMap.get(item.getId()), item);
            }
        });
        existedBusinessContactRelationList.forEach(item -> {
            if (contactRelationMap.containsKey(item.getId())) {
                ServiceUtils.myCopyProperties(contactRelationMap.get(item.getId()), item);
            }
        });
        existedContractBusinessRelationList.forEach(item -> {
            if (contractRelationMap.containsKey(item.getId())) {
                ServiceUtils.myCopyProperties(contractRelationMap.get(item.getId()), item);
            }
        });
        accountBusinessServiceTypeRelationRepository.deleteAllByIdInBatch(deleteServiceTypeRelationIds);
        accountBusinessAdministratorRepository.deleteAllByIdInBatch(deleteAdministratorIds);
        accountBusinessContactRelationRepository.deleteAllByIdInBatch(deleteContactRelationIds);
        contractBusinessRelationRepository.deleteAllByIdInBatch(deleteContractRelationIds);
        accountBusinessMigrateRepository.saveAll(businessList);
        accountBusinessServiceTypeRelationRepository.saveAll(serviceTypeRelationList);
        accountBusinessAdministratorRepository.saveAll(businessFlowAdministratorList);
        accountBusinessContactRelationRepository.saveAll(contactRelationList);
        contractBusinessRelationRepository.saveAll(contractRelationList);

        return businessList;
    }

    @Override
    public List<Long> getAllAmByCompany(Long companyId) {
        return accountBusinessAdministratorRepository.findAmIdsByCompanyId(companyId);
    }

    @Override
    public List<Long> getAllAmByJob(Long jobId) {
        return accountBusinessAdministratorRepository.findAmIdsByjobId(jobId);
    }

    @Override
    public Map<Long, JSONArray> getAmsGroupByCompany() {
        return accountBusinessAdministratorRepository.findCompanyToAmsMap(SecurityUtils.getTenantId()).stream().collect(Collectors.toMap(m->Long.valueOf(m[0].toString()), m->JSONArray.parseArray(m[1].toString())));
    }


    @Override
    public List<Map<String, Object>> getAmsGroupByCompanyFormat() {
        Map<Long, JSONArray> resultMap = getAmsGroupByCompany();
        if (CollUtil.isEmpty(resultMap)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> resultList = new ArrayList<>();
        resultMap.forEach((k, v) -> {
            Map<String, Object> map = new HashMap<>();
            map.put("companyId", k);
            map.put("data", v);
            resultList.add(map);
        });
        return resultList;
    }

    @Override
    public List<SalesLeadClientContact> findClientContactByTalentId(Long talentId) {
        return salesLeadClientContactRepository.findClientContactByTalentId(talentId);
    }

    @Override
    public List<AccountBusinessVO> queryBusinessListByCompanyId(Long companyId) {
        List<AccountBusiness> accountBusinessList = accountBusinessRepository.findAllByAccountCompanyIdAndBusinessProgressNot(companyId, BusinessProgress.ACCOUNT_BUSINESS_LOST.toDbValue());
        return toVo(accountBusinessList);
    }

    @Override
    public List<AccountBusiness> queryBusinessByBusinessUnitAndCountryLocation(String businessUnit, String countryLocation, Long companyId) {
        if (StringUtils.isNotBlank(businessUnit)) {
            List<AccountBusiness> accountBusinessList = accountBusinessRepository.findAllByBusinessUnitAndAccountCompanyId(businessUnit, companyId);
            return accountBusinessList;
        }
        List<AccountBusiness> accountBusinessList = accountBusinessRepository.findAllByAccountCompanyId(companyId);
        return accountBusinessList;
    }

    @Override
    public List<OwnerVO> getAllOwnerList() {
        List<UserBasicVM> userList = userServiceRepository.getAllOwnerList(SecurityUtils.getTenantId());
        return userList.stream().map(o -> new OwnerVO(o.getId(), CommonUtils.translateName(o.getFirstName(), o.getLastName()), o.isActivated())).toList();
    }

    @Override
    public String getAccountBusinessById(AccountBusinessDTO accountBusinessDTO) {
        List<AccountBusiness> accountBusinessList = accountBusinessRepository.findAllByIdIn(accountBusinessDTO.getIdList());
        JSONObject jsonObject = new JSONObject();
        for (AccountBusiness a : accountBusinessList) {
            String value ="";
            if(StringUtils.isNotEmpty(a.getName()) && !a.getName().equals("null")){
                value = a.getName();
            }
            jsonObject.put(a.getId() + "", value);
        }
        return jsonObject.toString();
    }

    //    private Company checkCompanyPermission(Long id) {
//        Company company = companyRepository.findById(id).orElseThrow(() -> new NotFoundException("company does not exists."));
//        if (!company.getTenantId().equals(SecurityUtils.getTenantId())) {
//            throw new ForbiddenException("company does not exists.");
//        }
//        return company;
//    }
//
//    private void checkCompanyServiceType(List<Long> ids) {
//        List<EnumCompanyServiceType> enumCompanyServiceTypeList = enumCompanyServiceTypeService.findAllByIds(ids);
//        if (enumCompanyServiceTypeList.size() != ids.size()) {
//            throw new CustomParameterizedException("serviceType does not exists.");
//        }
//    }
//
//    private void checkCompanyContact(List<Long> ids, Long companyId) {
//        List<SalesLeadClientContact> clientContactList = salesLeadClientContactRepository.findAllById(ids);
//        if (clientContactList.size() != ids.size() || clientContactList.stream().anyMatch(o -> !o.getCompanyId().equals(companyId))) {
//            throw new CustomParameterizedException("contact does not exists.");
//        }
//    }
//
//    private List<UserBriefDTO> checkSalesLeadUser(List<Long> ids) {
//        List<UserBriefDTO> userBriefDTOList = userService.getAllBriefUsersByIds(ids).getBody();
//        if (userBriefDTOList == null || userBriefDTOList.size() != ids.size() || userBriefDTOList.stream().anyMatch(o -> !o.getTenantId().equals(SecurityUtils.getTenantId()))) {
//            throw new CustomParameterizedException("user does not exists.");
//        }
//        return userBriefDTOList;
//    }
//
//    private void checkSalesLeadSource(List<Long> ids) {
//        List<EnumSalesLeadSource> enumSalesLeadSourceOldList = enumSalesLeadSourceService.findAllByIds(ids);
//        if (ids.size() != enumSalesLeadSourceOldList.size()) {
//            throw new CustomParameterizedException("salesLead does not exists.");
//        }
//    }

    private List<AccountBusinessVO> toVo(List<AccountBusiness> accountBusinessList) {
        List<AccountBusinessVO> accountBusinessVOList = new ArrayList<>();
        accountBusinessList.forEach(item -> {
            accountBusinessVOList.add(AccountBusiness.toAccountBusinessVO(item));
        });
        List<Long> businessIds = accountBusinessList.stream().map(AccountBusiness::getId).toList();
        CompletableFuture<List<AccountBusinessServiceTypeRelation>> serviceTypeRelationFuture = fetchServiceTypeRelationsAsync(businessIds);
        CompletableFuture<List<BusinessFlowAdministrator>> administratorFuture = fetchAdministratorAsync(businessIds);
        CompletableFuture<List<AccountBusinessContactRelation>> contactFuture = fetchContactAsync(businessIds);
        return CompletableFuture.allOf(serviceTypeRelationFuture, administratorFuture, contactFuture)
                .thenApply(voidResult -> {
                    List<AccountBusinessServiceTypeRelation> allServiceTypeRelationList = serviceTypeRelationFuture.join();
                    List<BusinessFlowAdministrator> allAdministratorList = administratorFuture.join();
                    List<AccountBusinessContactRelation> allContactRelationList = contactFuture.join();
                    Map<Long, List<AccountBusinessServiceTypeRelation>> serviceTypeRelationMap = allServiceTypeRelationList.stream().collect(Collectors.groupingBy(AccountBusinessServiceTypeRelation::getAccountBusinessId));
                    Map<Long, List<BusinessFlowAdministrator>> administratorMap = allAdministratorList.stream().collect(Collectors.groupingBy(BusinessFlowAdministrator::getAccountBusinessId));
                    Map<Long, List<AccountBusinessContactRelation>> contactRelationMap = allContactRelationList.stream().collect(Collectors.groupingBy(AccountBusinessContactRelation::getAccountBusinessId));
                    accountBusinessVOList.forEach(item -> {
                        if (serviceTypeRelationMap.containsKey(item.getId())) {
                            List<AccountBusinessServiceTypeRelation> serviceTypeRelationList = serviceTypeRelationMap.get(item.getId());
                            item.setCompanyServiceTypes(serviceTypeRelationList.stream().map(AccountBusinessServiceTypeRelation::getEnumId).toList());
                        }
                        if (administratorMap.containsKey(item.getId())) {
                            List<BusinessFlowAdministrator> administratorList = administratorMap.get(item.getId());
                            item.setAccountBusinessOwners(administratorList.stream().filter(o -> SalesLeadRoleType.BUSINESS_DEVELOPMENT.equals(o.getSalesLeadRoleType())).map(BusinessFlowAdministrator::toOwnerVO).toList());
                            item.setSalesLeadsOwners(administratorList.stream().filter(o -> SalesLeadRoleType.SALES_LEAD_OWNER.equals(o.getSalesLeadRoleType())).map(BusinessFlowAdministrator::toOwnerVO).toList());
                            item.setAccountManagers(administratorList.stream().filter(o -> SalesLeadRoleType.ACCOUNT_MANAGER.equals(o.getSalesLeadRoleType())).map(BusinessFlowAdministrator::toOwnerVO).toList());
                            item.setCooperateAccountManagers(administratorList.stream().filter(o -> SalesLeadRoleType.COOPERATE_ACCOUNT_MANAGER.equals(o.getSalesLeadRoleType())).map(BusinessFlowAdministrator::toOwnerVO).toList());
                            item.setRecruiter(administratorList.stream().filter(o -> SalesLeadRoleType.RECRUITER.equals(o.getSalesLeadRoleType())).map(BusinessFlowAdministrator::toOwnerVO).toList());
                        }
                        if (contactRelationMap.containsKey(item.getId())) {
                            List<AccountBusinessContactRelation> contactRelationList = contactRelationMap.get(item.getId());
                            item.setClientContacts(contactRelationList.stream().map(o -> new ContactVO(o.getClientContactId())).toList());
                        }
                    });
                    return accountBusinessVOList;
                })
                .join();
    }

    private CompletableFuture<List<AccountBusinessServiceTypeRelation>> fetchServiceTypeRelationsAsync(List<Long> businessIds) {
        return CompletableFuture.supplyAsync(() ->
                accountBusinessServiceTypeRelationRepository.findAllByAccountBusinessIdIn(businessIds)
        );
    }

    private CompletableFuture<List<BusinessFlowAdministrator>> fetchAdministratorAsync(List<Long> businessIds) {
        return CompletableFuture.supplyAsync(() ->
                accountBusinessAdministratorRepository.findAllByAccountBusinessIdInForCrm(businessIds)
        );
    }

    private CompletableFuture<List<AccountBusinessContactRelation>> fetchContactAsync(List<Long> businessIds) {
        return CompletableFuture.supplyAsync(() ->
                accountBusinessContactRelationRepository.findAllByAccountBusinessIdIn(businessIds)
        );
    }
}

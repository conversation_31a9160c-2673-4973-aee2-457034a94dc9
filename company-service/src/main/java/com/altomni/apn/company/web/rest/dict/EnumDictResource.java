package com.altomni.apn.company.web.rest.dict;

import com.altomni.apn.common.aop.cache.CacheControl;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.enums.*;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.dict.EnumDictVO;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.net.URISyntaxException;
import java.util.List;

@Api(tags = {"APN-Company-EnumDict"})
@Slf4j
@RestController
@RequestMapping("/api/v3/dict")
public class EnumDictResource {

    @Resource
    private EnumCompanyTagService enumCompanyTagService;

    @Resource
    private EnumCompanyContactTagService enumCompanyContactTagService;

    @Resource
    private EnumSalesLeadSourceService enumSalesLeadSourceService;

    @Resource
    private EnumCompanyServiceTypeService enumCompanyServiceTypeService;

    @Resource
    private EnumCountryService enumCountryService;

    /**
     * find country
     * @return
     */
    @GetMapping("/country")
    @CacheControl
    public ResponseEntity<List<EnumDictDTO>> findAllCountry(){
        log.info("[APN: Company @{}] REST request to find all country", SecurityUtils.getUserId());
        List<EnumDictDTO> result = enumCountryService.findAll();
        return ResponseEntity.ok().body(result);
    }

    /**
     * find all type
     * @return
     * @throws URISyntaxException
     */
    @GetMapping("/serviceTypes")
    @CacheControl
    public ResponseEntity<List<EnumDictVO>> findAllTypes(@RequestParam(name = "type", required = false) SortType type) throws URISyntaxException {
        log.info("[APN: Company @{}] REST request to find all company service types", SecurityUtils.getUserId());
        List<EnumDictVO> result = enumCompanyServiceTypeService.findAllTypeList(type);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("/salesLeadsSource")
    @CacheControl
    public ResponseEntity<List<EnumDictVO>> querySalesLeadSource(@RequestParam(name = "type", required = false) SortType type) {
        log.info("[APN: Company @{}] REST request to get all salesLead source .", SecurityUtils.getUserId());
        List<EnumDictVO> result = enumSalesLeadSourceService.querySalesLeadSource(type);
        return ResponseEntity.ok().body(result);
    }

    @ApiOperation(value = "Get/Filter request to get companyTag enum data . ")
    @GetMapping("/tags")
    @Timed
    @CacheControl
    public ResponseEntity<List<EnumDictVO>> getCompanyTag(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get companyTag dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumCompanyTagService.findAll(type), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to get companyContactTag enum data . ")
    @GetMapping("/contactTags")
    @Timed
    @CacheControl
    public ResponseEntity<List<EnumDictVO>> getCompanyContactTag(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get companyContactTag dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumCompanyContactTagService.findAll(type), HttpStatus.OK);
    }

}

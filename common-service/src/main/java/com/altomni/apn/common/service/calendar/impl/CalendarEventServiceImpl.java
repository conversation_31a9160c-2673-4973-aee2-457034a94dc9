package com.altomni.apn.common.service.calendar.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.StringUtils;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.LarkProperties;
import com.altomni.apn.common.config.env.CommonApiPromptProperties;
import com.altomni.apn.common.domain.calendar.CalendarEvent;
import com.altomni.apn.common.domain.calendar.CalendarEventAttendee;
import com.altomni.apn.common.domain.calendar.CalendarEventLog;
import com.altomni.apn.common.domain.calendar.CalendarEventRelationInfo;
import com.altomni.apn.common.domain.enumeration.SyncLarkEnum;
import com.altomni.apn.common.domain.enumeration.calendar.*;
import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.user.GetLastWeekActiveDurationUserInfoDTO;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.domain.xxljob.XxlJobRelation;
import com.altomni.apn.common.dto.calendar.*;
import com.altomni.apn.common.dto.customconfig.TenantConfigDTO;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobApnDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobApnParamDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateBySendTimeForJobAdminDTO;
import com.altomni.apn.common.enumeration.enums.CommonAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.repository.calendar.*;
import com.altomni.apn.common.repository.xxljob.XxlJobRelationRepository;
import com.altomni.apn.common.service.calendar.CalendarEventService;
import com.altomni.apn.common.service.lark.LarkClient;
import com.altomni.apn.common.service.report.ReportService;
import com.altomni.apn.common.service.talent.TalentFeignClient;
import com.altomni.apn.common.service.user.UserService;
import com.altomni.apn.common.service.xxljob.XxlJobClient;
import com.altomni.apn.common.service.xxljob.XxlJobHandlerService;
import com.altomni.apn.common.service.xxljob.XxlJobService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.common.vo.calendar.*;
import com.altomni.apn.common.vo.user.UserActiveDurationStatistic;
import com.altomni.apn.user.repository.customconfig.TenantPushRulesRepository;
import com.google.common.cache.Cache;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("calendarEventService")
public class CalendarEventServiceImpl implements CalendarEventService {

    @Resource
    private LarkProperties larkProperties;

    @Resource
    private CalendarEventRepository calendarEventRepository;

    @Resource
    private CalendarEventAttendeeRepository calendarEventAttendeeRepository;

    @Resource
    private CalendarEventCustomRepository calendarEventCustomRepository;

    @Resource
    private CalendarEventLogRepository calendarEventLogRepository;

    @Resource
    private CalendarEventRelationInfoRepository calendarEventRelationInfoRepository;

    @Resource
    private UserService userService;

    @Resource
    private LarkClient larkClient;

    @Resource
    private XxlJobService xxlJobService;

    @Resource
    private XxlJobRelationRepository xxlJobRelationRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CommonApiPromptProperties commonApiPromptProperties;

    @Resource
    private TalentFeignClient talentFeignClient;

    /**
     * 创建日程
     *
     * @param calendarEventDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCalendarEvent(CalendarEventDTO calendarEventDto) {
        // 检查 日历除外,其他想同类型的数据是否重复
        /*if (!Objects.equals(calendarEventDto.getTypeId(), CalendarEventTypeEnum.CALENDAR_EVENT.toDbValue())) {
            CalendarEvent calendarEvent = calendarEventRepository.findCalendarEventByTypeIdAndReferenceId(calendarEventDto.getTypeId(), calendarEventDto.getReferenceId());
            if (calendarEvent != null) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.CALENDER_CALENDAREVENTEXISTS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), commonApiPromptProperties.getCommonService()));
            }
        }*/

        if (calendarEventDto.getTenantId() == null) {
            calendarEventDto.setTenantId(SecurityUtils.getTenantId());
        }

        CalendarEvent calendarEvent = new CalendarEvent();
        BeanUtil.copyProperties(calendarEventDto, calendarEvent);
        calendarEvent.setTenantId(calendarEventDto.getTenantId());
        calendarEvent.setPriority(calendarEventDto.getPriority());
        calendarEvent.setCalendarType(calendarEventDto.getCalendarType());
        calendarEvent = calendarEventRepository.save(calendarEvent);
        Long eventId = calendarEvent.getId();
        List<CalendarEventAttendeeDTO> attendees = calendarEventDto.getAttendees();
        if (CollUtil.isNotEmpty(attendees)) {
            List<CalendarEventAttendee> calendarEventAttendeeList = new ArrayList<>();
            attendees.forEach(attendee -> {
                CalendarEventAttendee calendarEventAttendee = new CalendarEventAttendee();
                BeanUtil.copyProperties(attendee, calendarEventAttendee);
                calendarEventAttendee.setStatus(CalendarStatusEnum.TO_BE_COMPLETED);
                calendarEventAttendee.setEventId(eventId);
                calendarEventAttendeeList.add(calendarEventAttendee);
            });
            calendarEventAttendeeRepository.saveAll(calendarEventAttendeeList);
        }

        CalendarEventLog calendarEventLog = new CalendarEventLog();
        calendarEventLog.setNote(SecurityUtils.getUserName());
        calendarEventLog.setEventId(eventId);
        calendarEventLog.setOperateType("CREATE");
        calendarEventLogRepository.save(calendarEventLog);

        if (CollUtil.isNotEmpty(calendarEventDto.getRelationList())) {
            List<CalendarEventRelationInfo> calendarEventRelationInfos = new ArrayList<>();
            calendarEventDto.getRelationList().forEach(v -> {
                CalendarEventRelationInfo bean = new CalendarEventRelationInfo();
                bean.setEventId(eventId);
                bean.setRelationId(v.getRelationId());
                bean.setRelationName(v.getRelationName());
                bean.setRelationType(v.getRelationType());
                bean.setCompanyStatus(v.getCompanyStatus());
                calendarEventRelationInfos.add(bean);
            });
            calendarEventRelationInfoRepository.saveAll(calendarEventRelationInfos);
        }

        SecurityContext context = SecurityContextHolder.getContext();
        LarkClient.executorService.execute(() -> {
            //开始执行同步lark
            SecurityContextHolder.setContext(context);
            Integer syncLark = userService.getSyncLark().getBody();
            if (Objects.equals(SyncLarkEnum.SYNCHRONIZED.toDbValue(), syncLark)) {
                larkClient.createCalendarEventWithAttendees(calendarEventDto.getReminderMinutes(), calendarEventDto.getTitle(), calendarEventDto.getDescription(), calendarEventDto.getStartTime(), calendarEventDto.getEndTime(),
                        attendees.stream().map(CalendarEventAttendeeDTO::getEmail).toList(), eventId,
                        calendarLarkUpdateDto -> calendarEventRepository.updateLarkCalendarIdAndEventIdById(calendarLarkUpdateDto.getId(), calendarLarkUpdateDto.getLarkCalendarId(), calendarLarkUpdateDto.getLarkEventId()));
            }
        });
        if (calendarEventDto.getReminderMinutes() != null) {
            XxlJobClient.executorService.execute(() -> {
                //开始执行生成xxl-job
                SecurityContextHolder.setContext(context);
                addCalendarEventXxlJobRelation(eventId, calendarEventDto);
            });
        }
    }

    private void addCalendarEventXxlJobRelation(Long eventId, CalendarEventDTO calendarEventDto) {
        XxlJobApnDTO xxlJobApnDTO = new XxlJobApnDTO();
        XxlJobApnParamDTO xxlJobApnParamDTO = new XxlJobApnParamDTO();
        xxlJobApnParamDTO.setXxlJobType(XxlJobRelationTypeEnum.CALENDAR);
        xxlJobApnParamDTO.setReferenceId(eventId);
        xxlJobApnParamDTO.setUserId(SecurityUtils.getUserId());
        xxlJobApnParamDTO.setTenantId(SecurityUtils.getTenantId());
        xxlJobApnParamDTO.setSendTime(calendarEventDto.getStartTime().plus(-calendarEventDto.getReminderMinutes(), ChronoUnit.MINUTES));
        xxlJobApnParamDTO.setToken(SecurityUtils.getCurrentUserToken());
        xxlJobApnDTO.setXxlJobApnParamDTO(xxlJobApnParamDTO);
        Map<String, Object> paramMap = new JSONObject(xxlJobApnParamDTO);
        paramMap.put("eventId", eventId);
        xxlJobApnParamDTO.setXxlJobParam(paramMap);
        xxlJobApnDTO.setJobDesc(calendarEventDto.getTitle());
        xxlJobService.createXxlJob(xxlJobApnDTO);
    }

    public boolean isCurrentDateGreaterThan(Instant inputInstant) {
        String userTimeZone = SecurityUtils.getUserTimeZone();
        if(StringUtils.isEmpty(userTimeZone)) {
            userTimeZone = "UTC";
        }
        ZoneId zoneId = ZoneId.of(userTimeZone);
        ZonedDateTime zonedDateTime = inputInstant.atZone(zoneId);

        LocalDateTime currentDate = LocalDateTime.now(zoneId); // 获取当前日期

        return currentDate.isAfter(zonedDateTime.toLocalDateTime()); // 比较日期
    }

    /**
     * 修改入参信息
     *
     * @param calendarEventDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCalendarEvent(CalendarEventDTO calendarEventDto) {
        CalendarEvent exists = calendarEventRepository.findById(calendarEventDto.getId()).orElseThrow(() -> new NotFoundException("calendar event is no exists"));
        Instant oldStartTime = exists.getStartTime();
        Integer oldReminderMinutes = exists.getReminderMinutes();
        exists.setTitle(calendarEventDto.getTitle());
        exists.setDescription(calendarEventDto.getDescription());
        exists.setStartTime(calendarEventDto.getStartTime());
        exists.setEndTime(calendarEventDto.getEndTime());
        exists.setDescription(calendarEventDto.getDescription());
        exists.setReminderMinutes(calendarEventDto.getReminderMinutes());
        exists.setPriority(calendarEventDto.getPriority());
        exists.setCalendarType(calendarEventDto.getCalendarType());
        calendarEventRepository.save(exists);
        Boolean flag = isCurrentDateGreaterThan(calendarEventDto.getEndTime());
        List<CalendarEventAttendeeDTO> attendees = calendarEventDto.getAttendees();
        if (CollUtil.isNotEmpty(attendees)) {
            calendarEventAttendeeRepository.deleteByEventId(calendarEventDto.getId());
            List<CalendarEventAttendee> calendarEventAttendeeList = new ArrayList<>();
            attendees.forEach(attendee -> {
                CalendarEventAttendee calendarEventAttendee = new CalendarEventAttendee();
                BeanUtil.copyProperties(attendee, calendarEventAttendee);
                if (null != calendarEventDto.getStatus()) {
                    calendarEventAttendee.setStatus(calendarEventDto.getStatus());
                    if (calendarEventDto.getStatus().equals(CalendarStatusEnum.COMPLETED)) {
                        calendarEventAttendee.setCompletedTime(Instant.now());
                    }
                } else {
                    BeanUtil.copyProperties(attendee, calendarEventAttendee);
                    if (flag) {
                        calendarEventAttendee.setStatus(CalendarStatusEnum.OVERDUE);
                    } else {
                        calendarEventAttendee.setStatus(CalendarStatusEnum.TO_BE_COMPLETED);
                    }
                }
                calendarEventAttendee.setEventId(calendarEventDto.getId());
                calendarEventAttendeeList.add(calendarEventAttendee);
            });
            calendarEventAttendeeRepository.saveAll(calendarEventAttendeeList);
        }

        CalendarEventLog calendarEventLog = new CalendarEventLog();
        calendarEventLog.setNote(SecurityUtils.getUserName());
        calendarEventLog.setEventId(calendarEventDto.getId());
        calendarEventLog.setOperateType("MODIFY");
        calendarEventLogRepository.save(calendarEventLog);

        calendarEventRelationInfoRepository.deleteByEventId(calendarEventDto.getId());
        if (CollUtil.isNotEmpty(calendarEventDto.getRelationList())) {
            List<CalendarEventRelationInfo> calendarEventRelationInfos = new ArrayList<>();
            calendarEventDto.getRelationList().forEach(v -> {
                CalendarEventRelationInfo bean = new CalendarEventRelationInfo();
                bean.setEventId(calendarEventDto.getId());
                bean.setRelationId(v.getRelationId());
                bean.setRelationName(v.getRelationName());
                bean.setRelationType(v.getRelationType());
                bean.setCompanyStatus(v.getCompanyStatus());
                calendarEventRelationInfos.add(bean);
            });
            calendarEventRelationInfoRepository.saveAll(calendarEventRelationInfos);
        }

        SecurityContext context = SecurityContextHolder.getContext();
        LarkClient.executorService.execute(() -> {
            SecurityContextHolder.setContext(context);
            Integer syncLark = userService.getSyncLark().getBody();
            if (Objects.equals(SyncLarkEnum.SYNCHRONIZED.toDbValue(), syncLark)) {
                if (StrUtil.isBlank(exists.getLarkCalendarId())) {
                    larkClient.createCalendarEventWithAttendees(calendarEventDto.getReminderMinutes(), calendarEventDto.getTitle(), calendarEventDto.getDescription(), calendarEventDto.getStartTime(), calendarEventDto.getEndTime(),
                            attendees.stream().map(CalendarEventAttendeeDTO::getEmail).toList(), exists.getId(),
                            calendarLarkUpdateDto -> calendarEventRepository.updateLarkCalendarIdAndEventIdById(calendarLarkUpdateDto.getId(), calendarLarkUpdateDto.getLarkCalendarId(), calendarLarkUpdateDto.getLarkEventId()));

                } else {
                    larkClient.updateCalendarEventWithAttendees(calendarEventDto.getReminderMinutes(), calendarEventDto.getTitle(), calendarEventDto.getDescription(), calendarEventDto.getStartTime(), calendarEventDto.getEndTime(),
                            attendees.stream().map(CalendarEventAttendeeDTO::getEmail).toList(), exists.getLarkCalendarId(), exists.getLarkEventId());
                }
            }
        });
        if (!Objects.equals(oldStartTime, calendarEventDto.getStartTime())
                || !Objects.equals(oldReminderMinutes, calendarEventDto.getReminderMinutes())) {
            log.info("update calendar event, triggerTime is change, eventId = {}", calendarEventDto.getId());
            XxlJobClient.executorService.execute(() -> {
                SecurityContextHolder.setContext(context);
                XxlJobRelation xxlJobRelation = xxlJobRelationRepository.findXxlJobRelationByTypeAndReferenceId(XxlJobRelationTypeEnum.CALENDAR, calendarEventDto.getId());
                if (ObjectUtil.isEmpty(xxlJobRelation)) {
                    addCalendarEventXxlJobRelation(exists.getId(), calendarEventDto);
                } else {
                    //开始执行生成xxl-job
                    List<XxlJobUpdateBySendTimeForJobAdminDTO> paramDtoList = new ArrayList<>();
                    Instant triggerTimeInstant = calendarEventDto.getStartTime().plus(-calendarEventDto.getReminderMinutes(), ChronoUnit.MINUTES);
                    paramDtoList.add(XxlJobUpdateBySendTimeForJobAdminDTO.builder()
                            .xxlJobId(xxlJobRelation.getXxlJobId())
                            .timezone(xxlJobRelation.getTimezone())
                            .reminderConfig(xxlJobRelation.getReminderConfig() + "")
                            .sendTime(triggerTimeInstant)
                            .cron(DateUtil.getCron(triggerTimeInstant)).build());
                    xxlJobService.updateJobsBySendTime(paramDtoList);
                }
            });
        }
    }

    /**
     * 删除日程
     *
     * @param idList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCalendarEvent(List<Long> idList) {
        List<CalendarEvent> existList = calendarEventRepository.findAllByIdIn(idList);
        if (CollUtil.isEmpty(existList)) {
            return;
        }
        Long userId = SecurityUtils.getUserId();
        if (existList.stream().map(CalendarEvent::getPermissionUserId).anyMatch(permissionUserId -> !Objects.equals(permissionUserId, userId))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.CALENDER_DELETECALENDAREVENT_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), commonApiPromptProperties.getCommonService()));
        }
        existList.forEach(exists -> {
            calendarEventAttendeeRepository.deleteByEventId(exists.getId());
            calendarEventRepository.deleteById(exists.getId());
            SecurityContext context = SecurityContextHolder.getContext();
            LarkClient.executorService.execute(() -> {
                SecurityContextHolder.setContext(context);
                Integer syncLark = userService.getSyncLark().getBody();
                if (Objects.equals(SyncLarkEnum.SYNCHRONIZED.toDbValue(), syncLark)) {
                    larkClient.deleteCalendarEvent(exists.getLarkCalendarId(), exists.getLarkEventId());
                }
            });
            XxlJobClient.executorService.execute(() -> {
                SecurityContextHolder.setContext(context);
                XxlJobRelation xxlJobRelation = xxlJobRelationRepository.findXxlJobRelationByTypeAndReferenceId(XxlJobRelationTypeEnum.CALENDAR, exists.getId());
                xxlJobService.deleteXxlJob(xxlJobRelation.getXxlJobId());
            });
        });
    }

    @Override
    public void updateCalendarEventReminder(Long eventId, CalendarEventAttendeeReminderTypeEnum isReminder) {
        calendarEventAttendeeRepository.updateIsReminderByEventIdAndUserId(eventId, SecurityUtils.getUserId(), isReminder.toDbValue());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCalendarEventStatus(Long eventId, Long userId, CalendarStatusEnum status) {

        CalendarEventVO vo = getCalendarEventById(eventId);

        if (status.equals(CalendarStatusEnum.COMPLETED)) {
            calendarEventAttendeeRepository.updateStatusByEventIdAndTimeAndUserId(eventId, status.toDbValue());
        } else {

            boolean flag = isCurrentDateGreaterThan(vo.getEndTime());
            if(flag){
                calendarEventAttendeeRepository.updateStatusByEventIdAndUserId(eventId, CalendarStatusEnum.OVERDUE.toDbValue());
            }else{
                calendarEventAttendeeRepository.updateStatusByEventIdAndUserId(eventId, CalendarStatusEnum.TO_BE_COMPLETED.toDbValue());
            }

        }

        CalendarEventLog calendarEventLog = new CalendarEventLog();
        calendarEventLog.setNote(SecurityUtils.getUserName());
        calendarEventLog.setEventId(eventId);
        calendarEventLog.setOperateType("MODIFY");
        calendarEventLogRepository.save(calendarEventLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void systemCalendarEventComplete(CompleteSystemCalendarDTO dto) {
        try {
            List<Long> eventId = calendarEventRelationInfoRepository.findEventIdByUniqueReference(dto.getUniqueReferenceId(), dto.getType());
            if(eventId.isEmpty()) {
                return;
            }
            calendarEventAttendeeRepository.updateStatusByEventIdInAndTimeAndUserId(eventId, CalendarStatusEnum.COMPLETED.toDbValue());
            calendarEventRepository.updateLastModifiedDate(eventId);
        } catch (Exception e) {
            log.error("[APN] System calendar event complete error, request param： {}, error message: {}", JSONUtil.toJsonStr(dto), e.getMessage());
            String larkNotification = "System calendar event complete Error" +
                    "\n\tRequest data: " + JSONUtil.toJsonStr(dto) +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(larkProperties.getLarkWebhookKey(), larkProperties.getLarkWebhookUrl(), larkNotification);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAttendeesOverdueStatus() {
        log.info("[updateAttendeesOverdueStatus] start update calendar event attendee is status overdue");
        calendarEventAttendeeRepository.updateAttendeesOverdueStatus();
        log.info("[updateAttendeesOverdueStatus] end update calendar event attendee is status overdue");
    }

    @Override
    public CalendarEventSearchVO searchCalendarEventList(CalendarEventSearchDTO dto,
                                                         Pageable pageable) {
        CalendarEventSearchVO calendarEventSearchVO = calendarEventCustomRepository.searchCalendarEventList(dto, pageable);

        Set<Long> talentIds = calendarEventSearchVO.getEventVOList().stream().flatMap(v -> v.getTalentId().stream())
                .collect(Collectors.toSet());
        if (talentIds.isEmpty()) {
            return calendarEventSearchVO;
        }
        Map<Long, ConfidentialInfoDto> confidentialInfoMap = talentFeignClient.getTalentConfidentialInfo(talentIds).getBody();
        Set<Long> viewAbleConfidentialTalentIds = talentFeignClient.filterConfidentialTalentViewAble(talentIds).getBody();
        calendarEventSearchVO.getEventVOList().forEach(vo -> {
            if (vo.getTalentId().isEmpty()) {
                return;
            }
            vo.encrypt(confidentialInfoMap, viewAbleConfidentialTalentIds);
        });
        return calendarEventSearchVO;
    }

    @Override
    public CalendarEventVO getCalendarEventById(Long eventId) {
        CalendarEvent calendarEvent = calendarEventRepository.findById(eventId).orElseThrow(() -> new NotFoundException("calendar event is no exists"));
        CalendarEventVO vo = new CalendarEventVO();
        if (calendarEvent == null) {
            return vo;
        }

        vo.setCalendarType(calendarEvent.getCalendarType());
        vo.setPriority(calendarEvent.getPriority());
        vo.setAttendees(getCalendarEventAttendeeVOList(eventId));
        vo.setId(calendarEvent.getId());
        vo.setDescription(calendarEvent.getDescription());
        vo.setTenantId(calendarEvent.getTenantId());
        vo.setTitle(calendarEvent.getTitle());
        vo.setReferenceId(calendarEvent.getReferenceId());
        vo.setStartTime(calendarEvent.getStartTime());
        vo.setEndTime(calendarEvent.getEndTime());
        vo.setGoToId(calendarEvent.getGoToId());
        vo.setTypeId(calendarEvent.getTypeId());
        vo.setReminderMinutes(calendarEvent.getReminderMinutes());
        vo.setAttendees(getCalendarEventAttendeeVOList(eventId));

        List<CalendarEventLog> logList = calendarEventLogRepository.findAllByEventId(eventId);
        List<CalendarEventLogVO> voList = logList.stream().map(a -> formToVO(a.getCreatedDate(), a.getNote(),a.getOperateType())).sorted(Comparator.comparing(CalendarEventLogVO::getCreatedDate).reversed()).toList();
        vo.setLogs(voList);

        List<CalendarEventRelationInfo> relationInfoList = calendarEventRelationInfoRepository.findAllByEventId(eventId);
        List<CalendarEventRelationVO> relationVOList = relationInfoList.stream().map(a -> formToVO(a.getRelationId(), a.getRelationName(), a.getRelationType(),a.getCompanyStatus())).toList();
        vo.setRelations(relationVOList);
        return processConfidentialTalent(vo);
    }

    private CalendarEventVO processConfidentialTalent(CalendarEventVO vo) {
        List<CalendarEventRelationVO> relationVOList = vo.getRelations();
        Set<Long> talentIds = relationVOList.stream().filter(v -> CalendarRelationEnum.CANDIDATE.equals(v.getRelationType()))
                .map(CalendarEventRelationVO::getRelationId).collect(Collectors.toSet());
        Map<Long, ConfidentialInfoDto> confidentialInfoMap = talentFeignClient.getTalentConfidentialInfo(talentIds).getBody();
        Set<Long> viewAbleConfidentialTalentIds = talentFeignClient.filterConfidentialTalentViewAble(talentIds).getBody();
        relationVOList.stream().filter(v -> CalendarRelationEnum.CANDIDATE.equals(v.getRelationType()))
                .forEach(v -> {
                    if (!confidentialInfoMap.containsKey(v.getRelationId())) {
                        return;
                    }
                    // 日程中的保密候选人，只把 name 加密，其他的信息可以查看
                    if (!viewAbleConfidentialTalentIds.contains(v.getRelationId())) {
                        v.setRelationName("***");
                    }
                });
        return vo;
    }

    public CalendarEventRelationVO formToVO(Long id, String name, CalendarRelationEnum relationType,Integer companyStatus) {
        CalendarEventRelationVO vo = new CalendarEventRelationVO();
        vo.setRelationId(id);
        vo.setRelationType(relationType);
        vo.setRelationName(name);
        vo.setCompanyStatus(companyStatus);
        return vo;
    }

    public CalendarEventLogVO formToVO(Instant createdDate, String note,String operateType) {
        CalendarEventLogVO vo = new CalendarEventLogVO();
        vo.setNote(note);
        vo.setCreatedDate(createdDate);
        vo.setOperateType(operateType);
        return vo;
    }

    @Override
    public List<CalendarRecentDataVO> getRecentDateByType(CalendarRecentDataTypeEnum type) {
        List<CalendarRecentDataVO> infos = calendarEventCustomRepository.findAllByRelationType(SecurityUtils.getUserId(), type.toDbValue());
        return infos;
    }

    @Override
    public CalendarEventVO getCalendarEventByTypeIdAndReferenceId(Integer typeId, Long referenceId) {
        CalendarEvent calendarEvent = calendarEventRepository.findCalendarEventByTypeIdAndReferenceId(typeId, referenceId);
        CalendarEventVO vo = new CalendarEventVO();
        if (calendarEvent == null) {
            return vo;
        }
        vo.setCalendarType(calendarEvent.getCalendarType());
        vo.setPriority(calendarEvent.getPriority());
        vo.setAttendees(getCalendarEventAttendeeVOList(calendarEvent.getId()));
        vo.setId(calendarEvent.getId());
        vo.setDescription(calendarEvent.getDescription());
        vo.setTenantId(calendarEvent.getTenantId());
        vo.setTitle(calendarEvent.getTitle());
        vo.setReferenceId(calendarEvent.getReferenceId());
        vo.setStartTime(calendarEvent.getStartTime());
        vo.setEndTime(calendarEvent.getEndTime());
        vo.setGoToId(calendarEvent.getGoToId());
        vo.setTypeId(calendarEvent.getTypeId());
        vo.setReminderMinutes(calendarEvent.getReminderMinutes());
        vo.setAttendees(getCalendarEventAttendeeVOList(calendarEvent.getId()));

        List<CalendarEventLog> logList = calendarEventLogRepository.findAllByEventId(calendarEvent.getId());
        List<CalendarEventLogVO> voList = logList.stream().map(a -> formToVO(a.getCreatedDate(), a.getNote(),a.getOperateType())).sorted(Comparator.comparing(CalendarEventLogVO::getCreatedDate).reversed()).toList();
        vo.setLogs(voList);

        List<CalendarEventRelationInfo> relationInfoList = calendarEventRelationInfoRepository.findAllByEventId(calendarEvent.getId());
        List<CalendarEventRelationVO> relationVOList = relationInfoList.stream().map(a -> formToVO(a.getRelationId(), a.getRelationName(), a.getRelationType(),a.getCompanyStatus())).toList();
        vo.setRelations(relationVOList);
        return vo;
    }

    @Override
    public void syncOrCancelSyncCalendarEventToLark(Integer syncLark) {
        if (Objects.equals(syncLark, SyncLarkEnum.SYNCHRONIZED.toDbValue())) {
            SecurityContext context = SecurityContextHolder.getContext();
            List<CalendarEvent> calendarEventList = calendarEventRepository.findCalendarEventListByCreatedAndStartDate(SecurityUtils.getUserId());
            if (CollUtil.isNotEmpty(calendarEventList)) {
                calendarEventList.forEach(calendarEvent -> {
                    List<CalendarEventAttendeeVO> attendees = getCalendarEventAttendeeVOList(calendarEvent.getId());
                    List<Long> userIdList = attendees.stream().map(CalendarEventAttendeeVO::getUserId).collect(Collectors.toList());
                    List<UserBriefDTO> userBriefDTOList = userService.getBriefUsersByIds(userIdList).getBody();
                    LarkClient.executorService.execute(() -> {
                        //开始执行同步lark
                        SecurityContextHolder.setContext(context);
                        larkClient.createCalendarEventWithAttendees(calendarEvent.getReminderMinutes(), calendarEvent.getTitle(), calendarEvent.getDescription(), calendarEvent.getStartTime(), calendarEvent.getEndTime(),
                                userBriefDTOList.stream().map(UserBriefDTO::getEmail).toList(), calendarEvent.getId(),
                                calendarLarkUpdateDto -> calendarEventRepository.updateLarkCalendarIdAndEventIdById(calendarLarkUpdateDto.getId(), calendarLarkUpdateDto.getLarkCalendarId(), calendarLarkUpdateDto.getLarkEventId()));
                    });
                });
            }
        }
    }

    @Override
    public List<CalendarEventVO> checkDuplicateCalendarEvent(CalendarEventDTO calendarEventDto) {
        return calendarEventCustomRepository.checkDuplicateCalendarEvent(calendarEventDto);
    }

    @Override
    @Transactional
    public JSONArray deleteCalendarEventByRelationIds(List<Long> relationIds) {
        JSONArray result = new JSONArray();

        List<CalendarEvent> calendarEvents = calendarEventRepository.findAllByReferenceIdIn(relationIds);
        if (CollUtil.isEmpty(calendarEvents)) {
            return result;
        }

        Set<Long> eventIdSet = calendarEvents.stream().map(CalendarEvent::getId).collect(Collectors.toSet());
        List<CalendarEventRelationInfo> relationInfos = calendarEventRelationInfoRepository.findAllByEventIdIn(eventIdSet);
        List<CalendarEventAttendee> calendarEventAttendee = calendarEventAttendeeRepository.findAllByEventIdIn(new ArrayList<>(eventIdSet));

        Map<Long, List<CalendarEventRelationInfo>> relationInfoMap = relationInfos.stream().collect(Collectors.groupingBy(CalendarEventRelationInfo::getEventId));
        Map<Long, List<CalendarEventAttendee>> calendarEventAttendeeMap = calendarEventAttendee.stream().collect(Collectors.groupingBy(CalendarEventAttendee::getEventId));
        calendarEvents.forEach(calendarEvent -> {
            JSONObject item = DtoToJsonUtil.toJsonWithColumnNames(calendarEvent);
            if (relationInfoMap.containsKey(calendarEvent.getId())) {
                item.put("relations", DtoToJsonUtil.toJsonArrayWithColumnNames(relationInfoMap.get(calendarEvent.getId())));
            }
            if (calendarEventAttendeeMap.containsKey(calendarEvent.getId())) {
                item.put("calendarEventAttendee", DtoToJsonUtil.toJsonArrayWithColumnNames(calendarEventAttendeeMap.get(calendarEvent.getId())));
            }
            result.add(item);
        });
        calendarEventRepository.deleteAll(calendarEvents);
        calendarEventAttendeeRepository.deleteAll(calendarEventAttendee);
        return result;
    }

    @Resource
    private TenantPushRulesRepository tenantPushRulesRepository;

    @Override
    public PushRuleCheckVO pushRuleCheck() {
        PushRuleCheckVO ret = new PushRuleCheckVO();

        User user = userService.findUserById(SecurityUtils.getUserId()).getBody();
        if(user == null) {
            throw new NotFoundException("User is not exist.");
        }
        Integer pushCount = getPushCount(user);
        ret.setCount(pushCount);
        ret.setPush(pushCount > 0 && checkPush(user));
        return ret;
    }

    private Integer getPushCount(User user) {
        Long id = user.getId();
        Map<Long, SystemCalendarStatisticCacheDTO> systemCalendarStatistic = getSystemCalendarStatistic(List.of(id));
        SystemCalendarStatisticCacheDTO systemCalendarStatisticCacheDTO = systemCalendarStatistic.get(id);
        if(systemCalendarStatisticCacheDTO == null) {
            return 0;
        }
        Integer count = 0;
        ApplicationFollowStatistic applicationFollow = systemCalendarStatisticCacheDTO.getApplicationFollow();
        if(applicationFollow != null) {
            count += applicationFollow.getNotSubmitToClientTotal();
            count += applicationFollow.getSubmitToClientNotUpdateStatusTotal();
            count += applicationFollow.getOfferPassNotUpdateStatusTotal();
            count += applicationFollow.getPaymentOverdueTotal();
        }
        JobFollowStatistic jobFollow = systemCalendarStatisticCacheDTO.getJobFollow();
        if(jobFollow != null) {
            count += jobFollow.getNoSubmitTalentTotal();
            count += jobFollow.getNoInterviewTotal();
        }
        ClientContactFollowStatistic clientContactFollow = systemCalendarStatisticCacheDTO.getClientContactFollow();
        if(clientContactFollow != null) {
            count += clientContactFollow.getFollowUpRecordUpdateTotal();
            count += clientContactFollow.getExpectedOrderExpirationTotal();
            count += clientContactFollow.getContractNearingExpirationTotal();
            count += clientContactFollow.getContractExpiredTotal();
            count += clientContactFollow.getContactJobChangeTotal();
        }
        CalendarEventSearchDTO dto = new CalendarEventSearchDTO();
        dto.setStatus(List.of(CalendarStatusEnum.OVERDUE, CalendarStatusEnum.TO_BE_COMPLETED));
        dto.setCalendarType(List.of(CalendarTypeEnum.TASK,CalendarTypeEnum.CALL,CalendarTypeEnum.MEETING,CalendarTypeEnum.DEADLINE,CalendarTypeEnum.EMAIL,CalendarTypeEnum.LUNCH));
        dto.setUserIdList(List.of(SecurityUtils.getUserId()));
        String customTimezone = user.getCustomTimezone();
        if(StringUtils.isEmpty(customTimezone)) {
            customTimezone = "UTC";
        }
        ZoneId zoneId = ZoneId.of(customTimezone);

        // 获取今天和昨天的0点（当地时间）
        ZonedDateTime todayStart = ZonedDateTime.now(zoneId).toLocalDate().atStartOfDay(zoneId);

        // 结束时间设为今天0点减1秒
        ZonedDateTime endStart = todayStart.plusDays(1).minusSeconds(1);

        // 转换为UTC时间并格式化
        dto.setEndTime(endStart.toInstant());
        dto.setStartTime(todayStart.toInstant());
        count += calendarEventCustomRepository.countCalendarEventList(dto);
        return count;
    }

    private static final Map<Long, String> userToken = new ConcurrentHashMap<>();

    private Boolean checkPush(User user) {
        Long minInterval = tenantPushRulesRepository.findMinIntervalWithNullHandlingByUserIdAndTeamId(SecurityUtils.getUserId(), SecurityUtils.getTeamId(), Long.MAX_VALUE);
        if(null == minInterval) {
            return false;
        }
        if(Long.MAX_VALUE == minInterval) {
            return firstLogin(user);
        }
        return canPush(user, minInterval);
    }

    private boolean firstLogin(User user) {
        Instant tokenIssueTime = SecurityUtils.getTokenIssueTime();
        Instant lastPushTime = user.getLastPushTime();
        if(tokenIssueTime != null) {
            if(lastPushTime == null) {
                return true;
            } else {
                return tokenIssueTime.isAfter(lastPushTime);
            }
        } else {
            String currentUserToken = SecurityUtils.getCurrentUserToken();
            String preUserToken = userToken.get(SecurityUtils.getUserId());
            if(StringUtils.isEmpty(currentUserToken)) {
                return true;
            } else {
                userToken.put(SecurityUtils.getUserId(), currentUserToken);
                return !ObjectUtil.equal(preUserToken, currentUserToken);
            }
        }
    }

    @Resource
    private Cache<Long, TimestampedValue<SystemCalendarStatisticCacheDTO>> guavaTimestampedCache;

    @Override
    public SystemCalendarStatisticDTO systemCalendarStatistic(Boolean isTeam) {
        List<Long> userIds = isTeam ? getPermissionUserIds() : List.of(SecurityUtils.getUserId());
        Map<Long, SystemCalendarStatisticCacheDTO> systemCalendarStatistic = getSystemCalendarStatistic(userIds);
        SystemCalendarStatisticDTO systemCalendarStatisticDTO = mergeCacheDTO(systemCalendarStatistic, isTeam);
        userService.updatePushTime();
        if (!isTeam) {
            log.info("isTeam: {}, skip process confidential talents", isTeam);
            return systemCalendarStatisticDTO;
        }
        // 查看团队视图，才会涉及到保密候选人，因为个人视图里返回的候选人是从 kpi user 关联的，一定有保密查看权限
        log.info("process confidential talents");
        Set<Long> allTalentIds = systemCalendarStatisticDTO.allTalentIds();
        Map<Long, ConfidentialInfoDto> confidentialInfoMap = talentFeignClient.getTalentConfidentialInfo(allTalentIds).getBody();
        Set<Long> viewAbleConfidentialTalentIds = talentFeignClient.filterConfidentialTalentViewAble(allTalentIds).getBody();
        log.debug("confidentialInfoMap: {}, viewAbleConfidentialTalentIds: {}", confidentialInfoMap, viewAbleConfidentialTalentIds);
        return systemCalendarStatisticDTO.processConfidentialInfo(confidentialInfoMap, viewAbleConfidentialTalentIds);
    }

    @Override
    public List<SystemCalendarStatisticItem> getSystemCalendarStatisticByType(SystemCalendarStatisticByTypeDTO byTypeDTO) {
        List<Long> currentTeamUserIds = getPermissionUserIds();
        if (!currentTeamUserIds.contains(byTypeDTO.getUserId())) {
            throw new CustomParameterizedException("No permission to access this user");
        }

        TimestampedValue<SystemCalendarStatisticCacheDTO> ifPresent = guavaTimestampedCache.getIfPresent(byTypeDTO.getUserId());
        if (ifPresent == null) {
            return List.of();
        }

        SystemCalendarStatisticCacheDTO cacheDTO = ifPresent.getValue();
        List<SystemCalendarStatisticItem> result = switch (byTypeDTO.getType()) {
            case NOT_SUBMIT_TO_CLIENT -> cacheDTO.getApplicationFollow() != null ? cacheDTO.getApplicationFollow().getNotSubmitToClient() : null;
            case SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS -> cacheDTO.getApplicationFollow() != null ? cacheDTO.getApplicationFollow().getSubmitToClientNotUpdateStatus() : null;
            case OFFER_PASS_NOT_UPDATE_STATUS -> cacheDTO.getApplicationFollow() != null ? cacheDTO.getApplicationFollow().getOfferPassNotUpdateStatus() : null;
            case PAYMENT_OVERDUE -> cacheDTO.getApplicationFollow() != null ? cacheDTO.getApplicationFollow().getPaymentOverdue() : null;
            case FOLLOW_UP_RECORD_UPDATE -> cacheDTO.getClientContactFollow() != null ? cacheDTO.getClientContactFollow().getFollowUpRecordUpdate() : null;
            case EXPECTED_ORDER_EXPIRATION -> cacheDTO.getClientContactFollow() != null ? cacheDTO.getClientContactFollow().getExpectedOrderExpiration() : null;
            case CONTRACT_NEARING_EXPIRATION -> cacheDTO.getClientContactFollow() != null ? cacheDTO.getClientContactFollow().getContractNearingExpiration() : null;
            case CONTRACT_EXPIRED -> cacheDTO.getClientContactFollow() != null ? cacheDTO.getClientContactFollow().getContractExpired() : null;
            case CONTACT_JOB_CHANGE -> cacheDTO.getClientContactFollow() != null ? cacheDTO.getClientContactFollow().getContactJobChange() : null;
            case NO_SUBMIT_TALENT -> cacheDTO.getJobFollow() != null ? cacheDTO.getJobFollow().getNoSubmitTalent() : null;
            case NO_INTERVIEW -> cacheDTO.getJobFollow() != null ? cacheDTO.getJobFollow().getNoInterview() : null;
            default -> List.of();
        };

        if (result == null) {
            return List.of();
        }

        Set<Long> talentIds = result.stream()
                .map(SystemCalendarStatisticItem::getTalentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (talentIds.isEmpty()) {
            return result;
        }

        Map<Long, ConfidentialInfoDto> confidentialInfoMap = talentFeignClient.getTalentConfidentialInfo(talentIds).getBody();
        Set<Long> viewAbleConfidentialTalentIds = talentFeignClient.filterConfidentialTalentViewAble(talentIds).getBody();
        var processConfidential = SystemCalendarStatisticItem.processConfidential(confidentialInfoMap, viewAbleConfidentialTalentIds);

        return result.stream().map(processConfidential).toList();
    }

    @Override
    @Transactional
    public void batchSystemCalendar(BatchSystemCalendarDTO dto) {
        List<CalendarEventDTO> calendarEventDTOList = dto.getCalendarEventDTOList() == null ? new ArrayList<>() : dto.getCalendarEventDTOList();
        List<UpdateExistDTO> updateExistDTOList = dto.getUpdateExistDTOList() == null ? new ArrayList<>() : dto.getUpdateExistDTOList();
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            try {
                SecurityContextHolder.setContext(context);
                for(CalendarEventDTO calendarEventDTO : calendarEventDTOList) {
                    log.info("Batch add calendar, calendar DTO : {}", JsonUtil.toJson(calendarEventDTO));
                    createCalendarEvent(calendarEventDTO);
                }
                for(UpdateExistDTO updateExistDTO : updateExistDTOList) {
                    log.info("Batch update attendee calendar, update DTO : {}", JsonUtil.toJson(updateExistDTO));
                    List<CalendarEvent> eventList = calendarEventRepository.findUncompletedAndOverdueByCalendarTypeAndReferenceId(updateExistDTO.getCalendarTypeEnum(), updateExistDTO.getReferenceId());
                    updateAttendees(eventList, updateExistDTO.getAttendees());
                }
            } catch (Exception e) {
                log.error("[APN] System calendar event batch insert error, request param： {}, error message: {}", JSONUtil.toJsonStr(dto), e.getMessage());
                String larkNotification = "System calendar event batch insert Error" +
                        "\n\tRequest data: " + JSONUtil.toJsonStr(dto) +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(larkProperties.getLarkWebhookKey(), larkProperties.getLarkWebhookUrl(), larkNotification);
            }

        });
    }

    @Override
    public void updateAttendees(List<CalendarEvent> existEvent, List<CalendarEventAttendeeDTO> attendees) {
        if(existEvent == null || existEvent.isEmpty()) {
            return;
        }
        // 将入参的DTO转换为Map以便快速查找
        Map<Long, CalendarEventAttendeeDTO> attendeeDTOMap = attendees.stream()
                .filter(dto -> dto.getUserId() != null)
                .collect(Collectors.toMap(
                        CalendarEventAttendeeDTO::getUserId,
                        Function.identity(),
                        (existing, replacement) -> replacement // 如果有重复的userId，保留最后一个
                ));
        Map<Long, List<CalendarEventAttendee>> attendeeMap = calendarEventAttendeeRepository.findAllByEventIdIn(existEvent.stream().map(CalendarEvent::getId).toList()).stream()
                .collect(Collectors.groupingBy(CalendarEventAttendee::getEventId));
        Boolean update = false;
        for(CalendarEvent calendarEvent : existEvent) {
            List<CalendarEventAttendee> existingAttendees = attendeeMap.get(calendarEvent.getId());
            if(existingAttendees == null) {
                existingAttendees = new ArrayList<>();
            }
            // 需要删除的列表
            List<CalendarEventAttendee> toDelete = new ArrayList<>();

            CalendarStatusEnum statusEnum = CalendarStatusEnum.TO_BE_COMPLETED;
            Instant completedTime = null;
            // 遍历现有参与者，决定保留还是删除
            for(CalendarEventAttendee existing : existingAttendees) {
                statusEnum = existing.getStatus();
                completedTime = existing.getCompletedTime();
                CalendarEventAttendeeDTO dto = attendeeDTOMap.get(existing.getUserId());

                if(dto != null) {
                    // 从Map中移除已处理的DTO

                    attendeeDTOMap.remove(existing.getUserId());
                } else {
                    // 在新列表中不存在，标记为删除
                    toDelete.add(existing);
                }
            }

            final CalendarStatusEnum finalStatusEnum = statusEnum;
            final Instant finalCompletedTime = completedTime;
            // 处理新增的参与者
            List<CalendarEventAttendee> toAdd = attendeeDTOMap.values().stream()
                    .map(dto -> {
                        CalendarEventAttendee newAttendee = new CalendarEventAttendee();
                        newAttendee.setEventId(calendarEvent.getId());
                        newAttendee.setUserId(dto.getUserId());
                        newAttendee.setIsOrganizer(dto.getIsOrganizer());
                        newAttendee.setIsReminder(dto.getIsReminder());
                        newAttendee.setStatus(finalStatusEnum);
                        newAttendee.setCompletedTime(finalCompletedTime);
                        return newAttendee;
                    })
                    .collect(Collectors.toList());

            // 执行删除操作
            if(!toDelete.isEmpty()) {
                calendarEventAttendeeRepository.deleteAll(toDelete);
                update = true;
            }

            // 保存更新和新增的记录
            if(!toAdd.isEmpty()) {
                calendarEventAttendeeRepository.saveAll(toAdd);
                update = true;
            }

            // 为下一个事件重置Map
            attendeeDTOMap = attendees.stream()
                    .filter(dto -> dto.getUserId() != null)
                    .collect(Collectors.toMap(
                            CalendarEventAttendeeDTO::getUserId,
                            Function.identity(),
                            (existing, replacement) -> replacement
                    ));

        }
        //更新calendar_event编辑时间 避免修改了参与者 缓存未过期
        if(update) {
            calendarEventRepository.updateLastModifiedDate(existEvent.stream().map(CalendarEvent::getId).toList());
        }
    }

    /**
     * 保留Map中值最大的前3个键值对
     *
     * @param map 原始Map
     * @return 包含值最大的前3个键值对的新Map
     */
    private Map<Long, Long> keepTopThreeValues(Map<Long, Long> map) {
        if (map == null || map.size() <= 3) {
            return map; // 如果map为null或大小不超过3，则返回原map
        }

        // 使用Stream API按值降序排序，并限制为前3个
        return map.entrySet().stream()
                .sorted(Map.Entry.<Long, Long>comparingByValue().reversed())
                .limit(3)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,  // 合并函数，在此不会被调用
                        LinkedHashMap::new  // 使用LinkedHashMap保持排序
                ));
    }

    /**
     * 合并缓存的统计数据到一个统计DTO
     *
     * @param systemCalendarStatistic 用户ID到统计缓存DTO的映射
     * @return 合并后的统计DTO
     */
    private SystemCalendarStatisticDTO mergeCacheDTO(Map<Long, SystemCalendarStatisticCacheDTO> systemCalendarStatistic, Boolean isTeam) {
        // 创建返回对象
        SystemCalendarStatisticDTO result = new SystemCalendarStatisticDTO();

        if (systemCalendarStatistic == null || systemCalendarStatistic.isEmpty()) {
            return result;
        }

        // 初始化各统计Map
        Map<Long, ApplicationFollowStatistic> applicationFollowMap = new HashMap<>();
        ApplicationFollowUserCount applicationFollowUserCount = new ApplicationFollowUserCount();
        Map<Long, ClientContactFollowStatistic> clientContactFollowMap = new HashMap<>();
        ClientContactFollowUserCount clientContactFollowUserCount = new ClientContactFollowUserCount();
        Map<Long, JobFollowStatistic> jobFollowMap = new HashMap<>();
        JobFollowUserCount jobFollowUserCount = new JobFollowUserCount();

        // 统计总数
        SystemCalendarStatisticUserTotalDTO userTotalDTO = new SystemCalendarStatisticUserTotalDTO();
        long applicationFollowTotal = 0;
        long clientContactFollowTotal = 0;
        long jobFollowTotal = 0;

        // 遍历所有用户的统计数据
        for (Map.Entry<Long, SystemCalendarStatisticCacheDTO> entry : systemCalendarStatistic.entrySet()) {
            Long userId = entry.getKey();
            SystemCalendarStatisticCacheDTO cacheDTO = entry.getValue();

            if (cacheDTO == null) {
                continue;
            }

            if(Objects.equals(userId, SecurityUtils.getUserId())) {
                UserActiveDurationStatistic userActiveDuration = cacheDTO.getUserActiveDuration();
                UserActiveDurationStatistic durationStatistic = new UserActiveDurationStatistic();
                durationStatistic.setAverage(userActiveDuration.getAverage());
                if(isTeam) {
                    Map<Long, Long> lastWeekBelowAverage = userActiveDuration.getLastWeekBelowAverage();
                    if(lastWeekBelowAverage != null) {
                        filterInactiveUser(lastWeekBelowAverage);
                        durationStatistic.setLastWeekBelowAverage(keepTopThreeValues(lastWeekBelowAverage));
                        durationStatistic.setLastWeekBelowAverageCount(lastWeekBelowAverage.size());
                        result.setUserActiveDuration(durationStatistic);
                    }
                } else {
                    Map<Long, Long> lastWeekBelowAverage = userActiveDuration.getLastWeekBelowAverage();
                    if(lastWeekBelowAverage != null && lastWeekBelowAverage.containsKey(userId)) {
                        durationStatistic.setLastWeekBelowAverageCount(1);
                        Map<Long, Long> current = new HashMap<>();
                        current.put(userId, lastWeekBelowAverage.get(userId));
                        durationStatistic.setLastWeekBelowAverage(current);
                        result.setUserActiveDuration(durationStatistic);
                    }
                }
            }

            // 处理应用跟进统计
            if (cacheDTO.getApplicationFollow() != null) {
                ApplicationFollowStatistic afs = processApplicationFollow(cacheDTO.getApplicationFollow(), userTotalDTO);
                if (!isApplicationFollowEmpty(afs)) {
                    if(applicationFollowLimit(afs, applicationFollowUserCount)) {
                        applicationFollowMap.put(userId, afs);
                    }
                    applicationFollowTotal += countApplicationFollowItems(afs);
                }
            }

            // 处理客户联系人跟进统计
            if (cacheDTO.getClientContactFollow() != null) {
                ClientContactFollowStatistic ccfs = processClientContactFollow(cacheDTO.getClientContactFollow(), userTotalDTO);
                if (!isClientContactFollowEmpty(ccfs)) {
                    if(clientContactFollowLimit(ccfs, clientContactFollowUserCount)) {
                        clientContactFollowMap.put(userId, ccfs);
                    }
                    clientContactFollowTotal += countClientContactFollowItems(ccfs);
                }
            }

            // 处理职位跟进统计
            if (cacheDTO.getJobFollow() != null) {
                JobFollowStatistic jfs = processJobFollow(cacheDTO.getJobFollow(), userTotalDTO);
                if (!isJobFollowEmpty(jfs)) {
                    if(jobFollowLimit(jfs, jobFollowUserCount)) {
                        jobFollowMap.put(userId, jfs);
                    }
                    // 职位跟进还有额外的总数字段
                    Integer noSubmitTalentTotal = jfs.getNoSubmitTalentTotal();
                    Integer noInterviewTotal = jfs.getNoInterviewTotal();
                    jobFollowTotal += (noSubmitTalentTotal != null ? noSubmitTalentTotal : 0) +
                            (noInterviewTotal != null ? noInterviewTotal : 0);
                }
            }
        }

        // 设置结果
        result.setApplicationFollow(applicationFollowMap);
        result.setApplicationFollowTotal(applicationFollowTotal);

        result.setClientContactFollow(clientContactFollowMap);
        result.setClientContactFollowTotal(clientContactFollowTotal);

        result.setJobFollow(jobFollowMap);
        result.setJobFollowTotal(jobFollowTotal);


        TenantConfigDTO tenantConfigDTO = userService.getSettingConfig(TenantConfigCode.MESSAGE_CONFIG).getBody();
        Map<String, String> fieldMap = convertToFieldMap(tenantConfigDTO.getConfigValue());
        String noSubmit = fieldMap.get("POSITION_UNSUBMITTED_CANDIDATE_DAYS");
        if(StringUtils.isNotEmpty(noSubmit)) {
            result.setNoSubmitTalentReminderDay(Long.valueOf(noSubmit));
        }
        String noInterviewed = fieldMap.get("POSITION_UNINTERVIEWED_DAYS");
        if(StringUtils.isNotEmpty(noInterviewed)) {
            result.setNoInterviewReminderDay(Long.valueOf(noInterviewed));
        }
        result.setUserTotalStatistic(userTotalDTO);
        return result;
    }

    private boolean jobFollowLimit(JobFollowStatistic jfs, JobFollowUserCount jobFollowUserCount) {
        boolean valid = false;
        if(jobFollowUserCount.getNoSubmitTalentUserCount() < 3 && jfs.getNoSubmitTalentTotal() > 0) {
            jobFollowUserCount.setNoSubmitTalentUserCount(jobFollowUserCount.getNoSubmitTalentUserCount() + 1);
            valid = true;
        }
        if(jobFollowUserCount.getNoInterviewUserCount() < 3 && jfs.getNoInterviewTotal() > 0) {
            jobFollowUserCount.setNoInterviewUserCount(jobFollowUserCount.getNoInterviewUserCount() + 1);
            valid = true;
        }
        return valid;
    }
    private boolean applicationFollowLimit(ApplicationFollowStatistic afs, ApplicationFollowUserCount applicationFollowUserCount) {
        boolean valid = false;
        Integer paymentOverdueTotal = afs.getPaymentOverdueTotal();
        if(applicationFollowUserCount.getPaymentOverdueUserCount() < 3 && paymentOverdueTotal > 0) {
            applicationFollowUserCount.setPaymentOverdueUserCount(applicationFollowUserCount.getPaymentOverdueUserCount() + 1);
            valid = true;
        }
        Integer submitToClientNotUpdateStatusTotal = afs.getSubmitToClientNotUpdateStatusTotal();
        if(applicationFollowUserCount.getSubmitToClientNotUpdateStatusUserCount() < 3 && submitToClientNotUpdateStatusTotal > 0) {
            applicationFollowUserCount.setSubmitToClientNotUpdateStatusUserCount(applicationFollowUserCount.getSubmitToClientNotUpdateStatusUserCount() + 1);
            valid = true;
        }
        Integer notSubmitToClientTotal = afs.getNotSubmitToClientTotal();
        if(applicationFollowUserCount.getNotSubmitToClientUserCount() < 3 && notSubmitToClientTotal > 0) {
            applicationFollowUserCount.setNotSubmitToClientUserCount(applicationFollowUserCount.getNotSubmitToClientUserCount() + 1);
            valid = true;
        }
        Integer offerPassNotUpdateStatusTotal = afs.getOfferPassNotUpdateStatusTotal();
        if(applicationFollowUserCount.getOfferPassNotUpdateStatusUserCount() < 3 && offerPassNotUpdateStatusTotal > 0) {
            applicationFollowUserCount.setOfferPassNotUpdateStatusUserCount(applicationFollowUserCount.getOfferPassNotUpdateStatusUserCount() + 1);
            valid = true;
        }
        return valid;
    }

    private boolean clientContactFollowLimit(ClientContactFollowStatistic ccfs, ClientContactFollowUserCount clientContactFollowUserCount) {
        boolean valid = false;
        Integer followUpRecordUpdateTotal = ccfs.getFollowUpRecordUpdateTotal();
        if(clientContactFollowUserCount.getFollowUpRecordUpdateUserCount() < 3 && followUpRecordUpdateTotal > 0) {
            clientContactFollowUserCount.setFollowUpRecordUpdateUserCount(clientContactFollowUserCount.getFollowUpRecordUpdateUserCount() + 1);
            valid = true;
        }
        Integer expectedOrderExpirationTotal = ccfs.getExpectedOrderExpirationTotal();
        if(clientContactFollowUserCount.getExpectedOrderExpirationUserCount() < 3 && expectedOrderExpirationTotal > 0) {
            clientContactFollowUserCount.setExpectedOrderExpirationUserCount(clientContactFollowUserCount.getExpectedOrderExpirationUserCount() + 1);
            valid = true;
        }
        Integer contractNearingExpirationTotal = ccfs.getContractNearingExpirationTotal();
        if(clientContactFollowUserCount.getContractNearingExpirationUserCount() < 3 && contractNearingExpirationTotal > 0) {
            clientContactFollowUserCount.setContractNearingExpirationUserCount(clientContactFollowUserCount.getContractNearingExpirationUserCount() + 1);
            valid = true;
        }
        Integer contractExpiredTotal = ccfs.getContractExpiredTotal();
        if(clientContactFollowUserCount.getContractExpiredUserCount() < 3 && contractExpiredTotal > 0) {
            clientContactFollowUserCount.setContractExpiredUserCount(clientContactFollowUserCount.getContractExpiredUserCount() + 1);
            valid = true;
        }
        Integer contactJobChangeTotal = ccfs.getContactJobChangeTotal();
        if(clientContactFollowUserCount.getContactJobChangeUserCount() < 3 && contactJobChangeTotal > 0) {
            clientContactFollowUserCount.setContactJobChangeUserCount(clientContactFollowUserCount.getContactJobChangeUserCount() + 1);
            valid = true;
        }
        return valid;
    }

    public static Map<String, String> convertToFieldMap(String json) {
        Map<String, String> result = new HashMap<>();
        JSONArray array = JSONUtil.parseArray(json);

        for (int i = 0; i < array.size(); i++) {
            JSONObject obj = array.getJSONObject(i);
            result.put(
                    obj.getStr("field"),
                    obj.get("value").toString()
            );
        }

        return result;
    }

    private void filterInactiveUser(Map<Long, Long> lastWeekBelowAverage) {
        Set<Long> userIds = lastWeekBelowAverage.keySet();
        List<Long> inactive = userService.getAllInactiveByidIn(userIds).getBody();
        if(inactive != null && !inactive.isEmpty()) {
            for(Long inactiveId : inactive) {
                lastWeekBelowAverage.remove(inactiveId);
            }
        }
    }

    /**
     * 处理应用跟进统计，保留每个列表的前3条记录
     */
    private ApplicationFollowStatistic processApplicationFollow(ApplicationFollowStatistic original, SystemCalendarStatisticUserTotalDTO userTotalDTO) {
        if (original == null) {
            return new ApplicationFollowStatistic();
        }

        ApplicationFollowStatistic result = new ApplicationFollowStatistic();

        // 处理各子列表，保留前3条
        result.setNotSubmitToClient(limitListToThree(original.getNotSubmitToClient()));
        if(!original.getNotSubmitToClient().isEmpty()) {
            userTotalDTO.selfAddNotSubmitToClient();
            userTotalDTO.addNotSubmitToClientData(original.getNotSubmitToClient().size());
        }
        result.setSubmitToClientNotUpdateStatus(limitListToThree(original.getSubmitToClientNotUpdateStatus()));
        if(!original.getSubmitToClientNotUpdateStatus().isEmpty()) {
            userTotalDTO.selfAddSubmitToClientNotUpdateStatus();
            userTotalDTO.addSubmitToClientNotUpdateStatusData(original.getSubmitToClientNotUpdateStatus().size());
        }
        result.setOfferPassNotUpdateStatus(limitListToThree(original.getOfferPassNotUpdateStatus()));
        if(!original.getOfferPassNotUpdateStatus().isEmpty()) {
            userTotalDTO.selfAddOfferPassNotUpdateStatus();
            userTotalDTO.addOfferPassNotUpdateStatusData(original.getOfferPassNotUpdateStatus().size());
        }
        result.setPaymentOverdue(limitListToThree(original.getPaymentOverdue()));
        if(!original.getPaymentOverdue().isEmpty()) {
            userTotalDTO.selfAddPaymentOverdue();
            userTotalDTO.addPaymentOverdueData(original.getPaymentOverdue().size());
        }

        result.setNotSubmitToClientTotal(original.getNotSubmitToClientTotal());
        result.setSubmitToClientNotUpdateStatusTotal(original.getSubmitToClientNotUpdateStatusTotal());
        result.setOfferPassNotUpdateStatusTotal(original.getOfferPassNotUpdateStatusTotal());
        result.setPaymentOverdueTotal(original.getPaymentOverdueTotal());
        return result;
    }

    /**
     * 处理客户联系人跟进统计，保留每个列表的前3条记录
     */
    private ClientContactFollowStatistic processClientContactFollow(ClientContactFollowStatistic original, SystemCalendarStatisticUserTotalDTO userTotalDTO) {
        if (original == null) {
            return new ClientContactFollowStatistic();
        }

        ClientContactFollowStatistic result = new ClientContactFollowStatistic();

        // 处理各子列表，保留前3条
        result.setFollowUpRecordUpdate(limitListToThree(original.getFollowUpRecordUpdate()));
        if(!original.getFollowUpRecordUpdate().isEmpty()) {
            userTotalDTO.selfAddFollowUpRecordUpdate();
            userTotalDTO.addFollowUpRecordUpdateData(original.getFollowUpRecordUpdate().size());
        }
        result.setExpectedOrderExpiration(limitListToThree(original.getExpectedOrderExpiration()));
        if(!original.getExpectedOrderExpiration().isEmpty()) {
            userTotalDTO.selfAddExpectedOrderExpiration();
            userTotalDTO.addExpectedOrderExpirationData(original.getExpectedOrderExpiration().size());
        }
        result.setContractNearingExpiration(limitListToThree(original.getContractNearingExpiration()));
        if(!original.getContractNearingExpiration().isEmpty()) {
            userTotalDTO.selfAddContractNearingExpiration();
            userTotalDTO.addContractNearingExpirationData(original.getContractNearingExpiration().size());
        }
        result.setContractExpired(limitListToThree(original.getContractExpired()));
        if(!original.getContractExpired().isEmpty()) {
            userTotalDTO.selfAddContractExpired();
            userTotalDTO.addContractExpiredData(original.getContractExpired().size());
        }
        result.setContactJobChange(limitListToThree(original.getContactJobChange()));
        if(!original.getContactJobChange().isEmpty()) {
            userTotalDTO.selfAddContactJobChange();
            userTotalDTO.addContactJobChangeData(original.getContactJobChange().size());
        }

        result.setFollowUpRecordUpdateTotal(original.getFollowUpRecordUpdateTotal());
        result.setExpectedOrderExpirationTotal(original.getExpectedOrderExpirationTotal());
        result.setContractNearingExpirationTotal(original.getContractNearingExpirationTotal());
        result.setContractExpiredTotal(original.getContractExpiredTotal());
        result.setContactJobChangeTotal(original.getContactJobChangeTotal());

        return result;
    }

    /**
     * 处理职位跟进统计，保留每个列表的前3条记录但保留总数
     */
    private JobFollowStatistic processJobFollow(JobFollowStatistic original, SystemCalendarStatisticUserTotalDTO userTotalDTO) {
        if (original == null) {
            return new JobFollowStatistic();
        }

        JobFollowStatistic result = new JobFollowStatistic();

        // 处理各子列表，保留前3条
        result.setNoSubmitTalent(limitListToThree(original.getNoSubmitTalent()));
        if(!original.getNoSubmitTalent().isEmpty()) {
            userTotalDTO.selfAddNoSubmitTalentUser();
            userTotalDTO.addNoSubmitTalentData(original.getNoSubmitTalent().size());
        }
        result.setNoInterview(limitListToThree(original.getNoInterview()));
        if(!original.getNoInterview().isEmpty()) {
            userTotalDTO.selfAddNoInterviewUser();
            userTotalDTO.addNoInterviewData(original.getNoInterview().size());
        }

        // 保留总数
        result.setNoSubmitTalentTotal(original.getNoSubmitTalentTotal());
        result.setNoInterviewTotal(original.getNoInterviewTotal());

        return result;
    }

    /**
     * 限制列表大小为3，保留前3条记录
     */
    private <T> List<T> limitListToThree(List<T> list) {
        if (list == null || list.size() <= 3) {
            return list;
        }
        return list.subList(0, 3);
    }


    /**
     * 检查应用跟进统计是否为空
     */
    private boolean isApplicationFollowEmpty(ApplicationFollowStatistic afs) {
        return (afs.getNotSubmitToClient() == null || afs.getNotSubmitToClient().isEmpty()) &&
                (afs.getSubmitToClientNotUpdateStatus() == null || afs.getSubmitToClientNotUpdateStatus().isEmpty()) &&
                (afs.getOfferPassNotUpdateStatus() == null || afs.getOfferPassNotUpdateStatus().isEmpty()) &&
                (afs.getPaymentOverdue() == null || afs.getPaymentOverdue().isEmpty());
    }

    /**
     * 检查客户联系人跟进统计是否为空
     */
    private boolean isClientContactFollowEmpty(ClientContactFollowStatistic ccfs) {
        return (ccfs.getFollowUpRecordUpdate() == null || ccfs.getFollowUpRecordUpdate().isEmpty()) &&
                (ccfs.getExpectedOrderExpiration() == null || ccfs.getExpectedOrderExpiration().isEmpty()) &&
                (ccfs.getContractNearingExpiration() == null || ccfs.getContractNearingExpiration().isEmpty()) &&
                (ccfs.getContractExpired() == null || ccfs.getContractExpired().isEmpty()) &&
                (ccfs.getContactJobChange() == null || ccfs.getContactJobChange().isEmpty());
    }

    /**
     * 检查职位跟进统计是否为空
     */
    private boolean isJobFollowEmpty(JobFollowStatistic jfs) {
        boolean listsEmpty = (jfs.getNoSubmitTalent() == null || jfs.getNoSubmitTalent().isEmpty()) &&
                (jfs.getNoInterview() == null || jfs.getNoInterview().isEmpty());

        Integer noSubmitTalentTotal = jfs.getNoSubmitTalentTotal();
        Integer noInterviewTotal = jfs.getNoInterviewTotal();

        boolean totalsZero = (noSubmitTalentTotal == null || noSubmitTalentTotal == 0) &&
                (noInterviewTotal == null || noInterviewTotal == 0);

        return listsEmpty && totalsZero;
    }

    /**
     * 计算应用跟进统计中的项目总数
     */
    private long countApplicationFollowItems(ApplicationFollowStatistic afs) {
        long count = 0;
        count += afs.getNotSubmitToClientTotal();
        count += afs.getSubmitToClientNotUpdateStatusTotal();
        count += afs.getOfferPassNotUpdateStatusTotal();
        count += afs.getPaymentOverdueTotal();
        return count;
    }

    /**
     * 计算客户联系人跟进统计中的项目总数
     */
    private long countClientContactFollowItems(ClientContactFollowStatistic ccfs) {
        long count = 0;
        count += ccfs.getFollowUpRecordUpdateTotal();
        count += ccfs.getExpectedOrderExpirationTotal();
        count += ccfs.getContractNearingExpirationTotal();
        count += ccfs.getContractExpiredTotal();
        count += ccfs.getContactJobChangeTotal();
        return count;
    }

    private Map<Long, SystemCalendarStatisticCacheDTO> getSystemCalendarStatistic(List<Long> userIds) {
        List<CalendarEvent> calendarEvent = calendarEventRepository.findUncompletedAndOverdueSystemCalendarsByUserIds(userIds);
        List<Long> privateJobIds = new ArrayList<>();
        Map<Long, List<CalendarEventData>> userIdCalendarEventMap = getCalendarEventsGroupedByAttendees(calendarEvent, privateJobIds);
        Map<Long, UserBriefDTO> userMap = userService.getBriefUsersByIds(userIds).getBody().stream()
                .filter(user -> user != null && user.getId() != null)  // 过滤掉null和id为null的对象
                .collect(Collectors.toMap(
                        UserBriefDTO::getId,      // key映射函数 - 使用id作为key
                        Function.identity(),      // value映射函数 - 使用对象本身作为value
                        (existing, replacement) -> existing  // 如果有重复key，保留第一个遇到的值
                ));
        return userIds.stream()
                .collect(Collectors.toMap(
                        userId -> userId,  // 键为用户ID
                        userId -> getSystemCalendarStatisticFromCacheWithInit(userId, userMap, userIdCalendarEventMap, privateJobIds)  // 值为缓存数据
                ));
    }


    /**
     * 获取日历事件数据，按参与者用户ID分组
     *
     * @param events 日历事件列表
     * @return 用户ID到日历事件数据列表的映射
     */
    public Map<Long, List<CalendarEventData>> getCalendarEventsGroupedByAttendees(List<CalendarEvent> events, List<Long> privateJobIds) {
        if (events == null || events.isEmpty()) {
            return Collections.emptyMap();
        }

        // 获取所有事件ID
        List<Long> eventIds = events.stream().map(CalendarEvent::getId).toList();

        // 获取这些事件的所有参与者
        List<CalendarEventAttendee> attendees = calendarEventAttendeeRepository.findAllByEventIdIn(eventIds);
        // 获取这些事件的所有关系信息
        List<CalendarEventRelationInfo> relationInfoList = calendarEventRelationInfoRepository.findAllByEventIdIn(eventIds);

        List<Long> jobIds = relationInfoList.stream().filter(relationInfo -> relationInfo.getRelationType() == CalendarRelationEnum.JOB).map(CalendarEventRelationInfo::getRelationId).toList();
        List<Long> findPrivateJob = calendarEventRepository.findPrivateJobByIds(jobIds);
        if(findPrivateJob != null &&  !findPrivateJob.isEmpty()) {
            privateJobIds.addAll(findPrivateJob);
        }
        // 创建事件ID到事件对象的映射
        Map<Long, CalendarEvent> eventMap = events.stream()
                .collect(Collectors.toMap(CalendarEvent::getId, Function.identity()));

        // 创建事件ID到参与者列表的映射
        Map<Long, List<CalendarEventAttendee>> attendeeMap = attendees.stream()
                .collect(Collectors.groupingBy(CalendarEventAttendee::getEventId));

        // 创建事件ID到关系信息列表的映射
        Map<Long, List<CalendarEventRelationInfo>> relationInfoMap = relationInfoList.stream()
                .collect(Collectors.groupingBy(CalendarEventRelationInfo::getEventId));

        // 创建事件ID到CalendarEventData的映射
        Map<Long, CalendarEventData> eventDataMap = new HashMap<>();

        // 对每个事件创建CalendarEventData对象
        for (Long eventId : eventMap.keySet()) {
            CalendarEventData eventData = new CalendarEventData();
            eventData.setCalendarEvent(eventMap.get(eventId));
            eventData.setCalendarEventAttendeeList(attendeeMap.getOrDefault(eventId, new ArrayList<>()));
            eventData.setCalendarEventRelationInfoList(relationInfoMap.getOrDefault(eventId, new ArrayList<>()));

            eventDataMap.put(eventId, eventData);
        }

        // 创建结果Map: 用户ID -> 参与的日历事件数据列表
        Map<Long, List<CalendarEventData>> result = new HashMap<>();

        // 遍历所有参与者记录，按用户ID分组事件数据
        for (CalendarEventAttendee attendee : attendees) {
            // 获取用户ID和事件ID
            Long userId = attendee.getUserId();
            Long eventId = attendee.getEventId();

            // 获取对应的事件数据对象
            CalendarEventData eventData = eventDataMap.get(eventId);

            if (eventData != null) {
                // 如果用户ID还没有对应的列表，创建一个新列表
                if (!result.containsKey(userId)) {
                    result.put(userId, new ArrayList<>());
                }

                // 将事件数据添加到用户的事件数据列表中
                result.get(userId).add(eventData);
            }
        }

        return result;
    }

    private SystemCalendarStatisticCacheDTO getSystemCalendarStatisticFromCacheWithInit(Long userId, Map<Long, UserBriefDTO> userMap, Map<Long, List<CalendarEventData>> userIdCalendarEventMap, List<Long> privateJobIds) {
        TimestampedValue<SystemCalendarStatisticCacheDTO> cacheStatisticDTO = guavaTimestampedCache.getIfPresent(userId);
        UserBriefDTO userBriefDTO = userMap.get(userId);
        if(userBriefDTO == null) {
            throw new IllegalArgumentException("userId is not exist");
        }
        List<CalendarEventData> calendarEventData = userIdCalendarEventMap.get(userId);
        if(calendarEventData == null) {
            calendarEventData = new ArrayList<>();
        }
        Instant maxLastModifiedDate = getLatestModifiedDate(userId);
        if(cacheStatisticDTO != null) {
            Instant cacheTimestamp = cacheStatisticDTO.getTimestamp();
            if(maxLastModifiedDate.isAfter(cacheTimestamp)) {
                cacheStatisticDTO = new TimestampedValue<>(getSystemCalendarStatisticDTO(calendarEventData, privateJobIds), maxLastModifiedDate);
            } else {
                return cacheStatisticDTO.getValue();
            }
        } else {
            cacheStatisticDTO = new TimestampedValue<>(getSystemCalendarStatisticDTO(calendarEventData, privateJobIds), maxLastModifiedDate);
        }
        initUserActiveDuration(cacheStatisticDTO);
        guavaTimestampedCache.put(userId, cacheStatisticDTO);
        return cacheStatisticDTO.getValue();
    }

    @Resource
    private ReportService reportService;

    private void initUserActiveDuration(TimestampedValue<SystemCalendarStatisticCacheDTO> cacheStatisticDTO) {
        GetLastWeekActiveDurationUserInfoDTO dto = new GetLastWeekActiveDurationUserInfoDTO();
        UserActiveDurationStatistic statistic = reportService.getLastWeekActiveDurationUserInfo(dto).getBody();
        cacheStatisticDTO.getValue().setUserActiveDuration(statistic);
    }

    /**
     * 获取日历事件列表中的最新修改时间
     * @return 最新的修改时间，如果列表为空或所有修改时间为null则返回null
     */
    public Instant getLatestModifiedDate(Long userId) {
        Instant maxLatest = calendarEventRepository.findMaxLastModifiedDateForAllUserSystemCalendars(userId);

        return maxLatest == null ? Instant.MIN : maxLatest;
    }

    @Resource
    private XxlJobHandlerService xxlJobHandlerService;

    private SystemCalendarStatisticCacheDTO getSystemCalendarStatisticDTO(List<CalendarEventData> calendarEventData, List<Long> privateJobIds) {
        SystemCalendarStatisticCacheDTO ret = new SystemCalendarStatisticCacheDTO();
        // 初始化统计对象
        ApplicationFollowStatistic applicationFollow = new ApplicationFollowStatistic();
        ClientContactFollowStatistic clientContactFollow = new ClientContactFollowStatistic();
        JobFollowStatistic jobFollow = new JobFollowStatistic();

        // 遍历所有日历事件数据
        for (CalendarEventData eventData : calendarEventData) {
            CalendarEvent event = eventData.getCalendarEvent();
            List<CalendarEventRelationInfo> relationInfos = eventData.getCalendarEventRelationInfoList();

            // 跳过非系统日程
            if (event.getTypeId() == null || event.getTypeId() != 20) {
                continue;
            }

            // 根据日历类型分类处理
            CalendarTypeEnum calendarType = event.getCalendarType();
            if (calendarType == null) {
                continue;
            }

            // 创建并填充统计项
            SystemCalendarStatisticItem item = createStatisticItem(relationInfos, privateJobIds);

            // 根据日历类型，将统计项添加到相应的列表中
            switch (calendarType) {
                case NOT_SUBMIT_TO_CLIENT:
                    applicationFollow.getNotSubmitToClient().add(item);
                    applicationFollow.setNotSubmitToClientTotal(applicationFollow.getNotSubmitToClientTotal() + 1);
                    break;
                case SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS:
                    applicationFollow.getSubmitToClientNotUpdateStatus().add(item);
                    applicationFollow.setSubmitToClientNotUpdateStatusTotal(applicationFollow.getSubmitToClientNotUpdateStatusTotal() + 1);
                    break;
                case OFFER_PASS_NOT_UPDATE_STATUS:
                    applicationFollow.getOfferPassNotUpdateStatus().add(item);
                    applicationFollow.setOfferPassNotUpdateStatusTotal(applicationFollow.getOfferPassNotUpdateStatusTotal() + 1);
                    break;
                case PAYMENT_OVERDUE:
                    applicationFollow.getPaymentOverdue().add(item);
                    applicationFollow.setPaymentOverdueTotal(applicationFollow.getPaymentOverdueTotal() + 1);
                    break;
                case FOLLOW_UP_RECORD_UPDATE:
                    clientContactFollow.getFollowUpRecordUpdate().add(item);
                    clientContactFollow.setFollowUpRecordUpdateTotal(clientContactFollow.getFollowUpRecordUpdateTotal() + 1);
                    break;
                case EXPECTED_ORDER_EXPIRATION:
                    clientContactFollow.getExpectedOrderExpiration().add(item);
                    clientContactFollow.setExpectedOrderExpirationTotal(clientContactFollow.getExpectedOrderExpirationTotal() + 1);
                    break;
                case CONTRACT_NEARING_EXPIRATION:
                    clientContactFollow.getContractNearingExpiration().add(item);
                    clientContactFollow.setContractNearingExpirationTotal(clientContactFollow.getContractNearingExpirationTotal() + 1);
                    break;
                case CONTRACT_EXPIRED:
                    clientContactFollow.getContractExpired().add(item);
                    clientContactFollow.setContractExpiredTotal(clientContactFollow.getContractExpiredTotal() + 1);
                    break;
                case CONTACT_JOB_CHANGE:
                    clientContactFollow.getContactJobChange().add(item);
                    clientContactFollow.setContactJobChangeTotal(clientContactFollow.getContactJobChangeTotal() + 1);
                    break;
                case NO_SUBMIT_TALENT:
                    jobFollow.getNoSubmitTalent().add(item);
                    jobFollow.setNoSubmitTalentTotal(jobFollow.getNoSubmitTalentTotal() + 1);
                    break;
                case NO_INTERVIEW:
                    jobFollow.getNoInterview().add(item);
                    jobFollow.setNoInterviewTotal(jobFollow.getNoInterviewTotal() + 1);
                    break;
                default:
                    // 不处理其他类型
                    break;
            }
        }

        // 设置统计结果
        applicationFollow.count();
        clientContactFollow.count();
        jobFollow.count();
        ret.setApplicationFollow(applicationFollow);
        ret.setClientContactFollow(clientContactFollow);
        ret.setJobFollow(jobFollow);

        return ret;
    }

    /**
     * 创建系统日历统计项
     */
    private SystemCalendarStatisticItem createStatisticItem(List<CalendarEventRelationInfo> relationInfos, List<Long> privateJobIds) {
        SystemCalendarStatisticItem item = new SystemCalendarStatisticItem();

        // 从关联信息中提取数据
        if (relationInfos != null && !relationInfos.isEmpty()) {
            for (CalendarEventRelationInfo info : relationInfos) {
                CalendarRelationEnum relationType = info.getRelationType();
                if (relationType == null) {
                    continue;
                }

                switch (relationType) {
                    case COMPANY:
                        item.setCompanyId(info.getRelationId());
                        item.setCompanyName(info.getRelationName());
                        break;
                    case COMPANY_CONTACT:
                        item.setContactId(info.getRelationId());
                        // 客户联系人相关信息
                        item.setContactName(info.getRelationName());
                        break;
                    case CANDIDATE:
                        item.setTalentId(info.getRelationId());
                        item.setTalentName(info.getRelationName());
                        break;
                    case JOB:
                        item.setJobId(info.getRelationId());
                        // 优先使用关联信息中的名称，如果没有则从map中获取
                        item.setJobName(info.getRelationName());
                        item.setJobPrivate(privateJobIds.contains(info.getRelationId()));
                        break;
                    default:
                        // 不处理其他类型
                        break;
                }
            }
        }

        return item;
    }

    private List<Long> getPermissionUserIds() {
        if(SecurityUtils.getTeamId() == null) {
            return new ArrayList<>();
        }
        Set<Long> teamUserIds = userService.getAllActiveTeamUserIdsByPermissionTeamIdIn(Set.of(SecurityUtils.getTeamId())).getBody();
        return teamUserIds == null ? new ArrayList<>() : new ArrayList<>(teamUserIds);
    }

    // 使用Duration计算时间差
    public boolean canPush(User user, Long interval) {
        List<CalendarEvent> exist = calendarEventRepository.findUncompletedAndOverdueSystemCalendarsByUserIds(List.of(user.getId()));
        if(exist.isEmpty()) {
            return false;
        }
        if (user.getLastPushTime() == null) {
            return true; // 如果从未推送过，则允许推送
        }

        if (interval == null) {
            return false; // 如果没有设置间隔限制，则不推送
        }

        Instant now = Instant.now();
        Duration timeSinceLastPush = Duration.between(user.getLastPushTime(), now);
        long minutesSinceLastPush = timeSinceLastPush.toMinutes();

        return minutesSinceLastPush >= interval;
    }

    public List<CalendarEventAttendeeVO> getCalendarEventAttendeeVOList(Long eventId) {
        List<CalendarEventAttendee> calendarEventAttendeeList = calendarEventAttendeeRepository.findAllByEventId(eventId);
        return calendarEventAttendeeList.stream().map(a -> {
            CalendarEventAttendeeVO calendarEventAttendeeVO = new CalendarEventAttendeeVO();
            BeanUtil.copyProperties(a, calendarEventAttendeeVO);
            return calendarEventAttendeeVO;
        }).toList();
    }

}
